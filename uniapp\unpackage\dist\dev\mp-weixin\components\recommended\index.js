"use strict";
const api_goods_index = require("../../api/goods/index.js");
const common_enum_setting_Key = require("../../common/enum/setting/Key.js");
const common_model_Setting = require("../../common/model/Setting.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "Recommended",
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 商品列表
      goodsList: [],
      // 推荐商品设置
      setting: {
        enabled: 1,
        style: {
          show: []
        }
      }
    };
  },
  async created() {
    const app = this;
    app.isLoading = true;
    await app.getSetting();
    if (app.setting.enabled && app.$checkModule("market-recommended")) {
      await app.getRecommended();
    }
    app.isLoading = false;
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 获取推荐商品列表
    async getRecommended() {
      const app = this;
      await api_goods_index.recommended().then((result) => app.goodsList = result.data.goodsList);
    },
    // 获取商城设置
    async getSetting() {
      const app = this;
      await common_model_Setting.SettingModel.item(common_enum_setting_Key.SettingKeyEnum.RECOMMENDED.value).then((setting) => app.setting = setting);
    },
    // 跳转商品详情页
    onTargetGoods(goodsId) {
      this.$navTo(`pages/goods/detail`, { goodsId });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading && $data.setting.enabled && _ctx.$checkModule("market-recommended") && $data.goodsList.length
  }, !$data.isLoading && $data.setting.enabled && _ctx.$checkModule("market-recommended") && $data.goodsList.length ? {
    b: common_vendor.t($data.setting.style.title),
    c: common_vendor.f($data.goodsList, (dataItem, index, i0) => {
      return common_vendor.e($data.setting.style.column === 1 ? common_vendor.e({
        a: dataItem.goods_image,
        b: $data.setting.style.show.includes("goodsName")
      }, $data.setting.style.show.includes("goodsName") ? {
        c: common_vendor.t(dataItem.goods_name)
      } : {}, {
        d: $data.setting.style.show.includes("sellingPoint")
      }, $data.setting.style.show.includes("sellingPoint") ? {
        e: common_vendor.t(dataItem.selling_point)
      } : {}, {
        f: $data.setting.style.show.includes("goodsSales")
      }, $data.setting.style.show.includes("goodsSales") ? {
        g: common_vendor.t(dataItem.goods_sales)
      } : {}, {
        h: $data.setting.style.show.includes("goodsPrice")
      }, $data.setting.style.show.includes("goodsPrice") ? {
        i: common_vendor.t(dataItem.goods_price_min)
      } : {}, {
        j: $data.setting.style.show.includes("linePrice") && dataItem.line_price_min > 0
      }, $data.setting.style.show.includes("linePrice") && dataItem.line_price_min > 0 ? {
        k: common_vendor.t(dataItem.line_price_min)
      } : {}) : common_vendor.e({
        l: dataItem.goods_image,
        m: $data.setting.style.show.includes("goodsName")
      }, $data.setting.style.show.includes("goodsName") ? {
        n: common_vendor.t(dataItem.goods_name)
      } : {}, {
        o: $data.setting.style.show.includes("goodsPrice")
      }, $data.setting.style.show.includes("goodsPrice") ? {
        p: common_vendor.t(dataItem.goods_price_min)
      } : {}, {
        q: $data.setting.style.show.includes("linePrice") && dataItem.line_price_min > 0
      }, $data.setting.style.show.includes("linePrice") && dataItem.line_price_min > 0 ? {
        r: common_vendor.t(dataItem.line_price_min)
      } : {}), {
        s: index,
        t: common_vendor.o(($event) => $options.onTargetGoods(dataItem.goods_id), index)
      });
    }),
    d: $data.setting.style.column === 1,
    e: common_vendor.n(`display__${$data.setting.style.display}`),
    f: common_vendor.n(`column__${$data.setting.style.column}`),
    g: common_vendor.s(_ctx.appThemeStyle)
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8e960390"]]);
wx.createComponent(Component);
