"use strict";
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_article_index = require("../../api/article/index.js");
const api_article_category = require("../../api/article/category.js");
const core_app = require("../../core/app.js");
const common_vendor = require("../../common/vendor.js");
const pageSize = 15;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 选项卡列表
      tabList: [],
      // 当前选项
      curTab: 0,
      // 当前文章分类ID
      categoryId: 0,
      // 文章列表
      articleList: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于3条才显示无更多数据
        noMoreSize: 3
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const app = this;
    app.categoryId = options.categoryId || 0;
    app.getCategoryList();
    app.setWxofficialShareData();
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getArticleList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取文章分类数据
    getCategoryList() {
      api_article_category.list().then((result) => {
        this.setTabList(result.data.list);
      });
    },
    // 设置选项卡数据
    setTabList(categoryList) {
      const app = this;
      app.tabList = [{ value: 0, name: "全部" }];
      categoryList.forEach((item) => {
        app.tabList.push({ value: item.category_id, name: item.name });
      });
      if (app.categoryId > 0) {
        const findIndex = app.tabList.findIndex((item) => item.value == app.categoryId);
        app.curTab = findIndex > -1 ? findIndex : 0;
      }
    },
    /**
     * 获取文章列表
     * @param {Number} pageNo 页码
     */
    getArticleList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_article_index.list({ categoryId: app.categoryId, page: pageNo }, { load: false }).then((result) => {
          const newList = result.data.list;
          app.articleList.data = core_app.getMoreListData(newList, app.articleList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 切换标签项
    onChangeTab(index) {
      this.curTab = index;
      this.categoryId = this.tabList[index].value;
      this.onRefreshList();
    },
    // 刷新列表数据
    onRefreshList() {
      this.articleList = core_app.getEmptyPaginateObj();
      setTimeout(() => this.mescroll.resetUpScroll(), 120);
    },
    // 跳转文章详情页
    onTargetDetail(articleId) {
      this.$navTo("pages/article/detail", { articleId });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({ title: "文章首页" });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: "文章首页",
      path: "/pages/article/index?" + this.$getShareUrlParams()
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: "文章首页",
      path: "/pages/article/index?" + this.$getShareUrlParams()
    };
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_easycom_u_tabs2 + _easycom_mescroll_body2)();
}
const _easycom_u_tabs = () => "../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_mescroll_body)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabList,
      ["is-scroll"]: true,
      ["active-color"]: _ctx.appTheme.mainBg,
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.articleList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.show_type == 10
      }, item.show_type == 10 ? {
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.show_views),
        d: item.image_url
      } : {}, {
        e: item.show_type == 20
      }, item.show_type == 20 ? {
        f: common_vendor.t(item.title),
        g: item.image_url,
        h: common_vendor.t(item.show_views)
      } : {}, {
        i: common_vendor.n(`show-type__${item.show_type}`),
        j: index,
        k: common_vendor.o(($event) => $options.onTargetDetail(item.article_id), index)
      });
    }),
    e: common_vendor.sr("mescrollRef", "fd1fc04a-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o($options.upCallback),
    h: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    i: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fd1fc04a"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
