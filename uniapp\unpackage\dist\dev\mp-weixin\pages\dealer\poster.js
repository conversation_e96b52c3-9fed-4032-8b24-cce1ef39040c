"use strict";
const common_vendor = require("../../common/vendor.js");
const api_dealer_poster = require("../../api/dealer/poster.js");
const common_model_dealer_Setting = require("../../common/model/dealer/Setting.js");
const _sfc_main = {
  data() {
    return {
      // 海报图url
      imageUrl: void 0
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getSetting();
    this.getPoster();
  },
  methods: {
    // 获取分销设置
    getSetting() {
      const app = this;
      common_model_dealer_Setting.SettingModel.data().then((setting) => {
        const words = setting.words.poster;
        app.setPageTitle(words.title);
      });
    },
    // 获取推广二维码
    getPoster() {
      const app = this;
      app.isLoading = true;
      api_dealer_poster.qrcode({ channel: app.platform }).then((result) => {
        app.imageUrl = result.data.imageUrl;
      }).finally(() => app.isLoading = false);
    },
    // 设置当前页面标题
    setPageTitle(title) {
      common_vendor.index.setNavigationBarTitle({ title: title.value });
    },
    // 预览海报图
    onPreviewImage() {
      common_vendor.index.previewImage({
        current: this.imageUrl,
        urls: [this.imageUrl]
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.imageUrl
  }, $data.imageUrl ? {
    b: $data.imageUrl,
    c: common_vendor.o((...args) => $options.onPreviewImage && $options.onPreviewImage(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3b46ef68"]]);
wx.createPage(MiniProgramPage);
