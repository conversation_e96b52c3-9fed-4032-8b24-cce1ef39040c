"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    // 正在加载
    isLoading: {
      type: Boolean,
      default: false
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default() {
        return {};
      }
    },
    // 提示的问题
    tips: {
      type: String,
      default: "亲，暂无相关数据"
    }
  },
  data() {
    return {};
  },
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$props.isLoading
  }, !$props.isLoading ? {
    b: common_assets._imports_0$5,
    c: common_vendor.t($props.tips),
    d: common_vendor.s($props.customStyle)
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c00e6953"]]);
wx.createComponent(Component);
