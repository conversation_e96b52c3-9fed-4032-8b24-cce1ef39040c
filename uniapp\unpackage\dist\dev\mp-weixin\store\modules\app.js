"use strict";
const store_mutationTypes = require("../mutation-types.js");
const utils_storage = require("../../utils/storage.js");
const app = {
  state: {
    // 当前商城的ID
    storeId: null,
    // 当前终端平台
    platform: "",
    // 推荐人ID
    refereeId: null,
    // 开启的功能模块
    modules: []
  },
  mutations: {
    SET_STORE_ID: (state, value) => {
      state.storeId = value;
    },
    SET_PLATFORM: (state, value) => {
      state.platform = value;
    },
    SET_REFEREE_ID: (state, value) => {
      state.refereeId = value;
    },
    SET_MODULES: (state, value) => {
      state.modules = value;
    }
  },
  actions: {
    // 记录推荐人ID
    setRefereeId({ commit }, value) {
      const store = this;
      const refereeId = parseInt(value);
      return new Promise((resolve, reject) => {
        if (refereeId > 0 && store.getters.userId != refereeId) {
          utils_storage.storage.set(store_mutationTypes.REFEREE_ID, refereeId);
          commit("SET_REFEREE_ID", refereeId);
          resolve();
        }
      });
    },
    // 记录开启的功能模块
    SetModules({ commit }, modules) {
      return new Promise((resolve, reject) => {
        utils_storage.storage.set(store_mutationTypes.MODULES, modules);
        commit("SET_MODULES", modules);
        resolve();
      });
    }
  }
};
exports.app = app;
