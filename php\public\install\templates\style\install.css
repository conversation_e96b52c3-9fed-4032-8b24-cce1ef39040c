@charset "utf-8";

/* 全局控制 */
html {
    /*overflow: hidden;*/
}

body {
    margin: 0;
    padding: 0;
    font-family: "Microsoft YaHei", <PERSON><PERSON>, "宋体";
    font-size: 13px;
    color: #333;
}

html, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, p {
    margin: 0;
    padding: 0;
}

input, select, textarea {
    vertical-align: middle;
    font-family: "Microsoft YaHei", Arial, "宋体";
    font-size: 12px;
}

img {
    border: 0;
}

ul, li {
    list-style-type: none;
}

a {
    color: #333;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
    color: #333;
}

a:focus {
    outline: none;
}

a {
    transition-duration: 300ms;
}


/*全局函数*/
.hr_1, .hr_8, .hr_10 {
    font-size: 1px;
    line-height: 1px;
    clear: both;
    overflow: hidden;
}

.hr_1 {
    height: 1px;
}

.hr_8 {
    height: 8px;
}

.hr_10 {
    height: 10px;
}

.col-red {
    color: red;
}

/*定义新型浏览器特性*/
::-webkit-scrollbar {
    width: 10px;
    height: 10px
}

::-webkit-scrollbar-button:vertical {
    display: none
}

::-webkit-scrollbar-track:vertical {
    background: #000
}

::-webkit-scrollbar-track-piece {
    background: #f6f6f6
}

::-webkit-scrollbar-thumb:vertical {
    background: #d0d0d0;
}

::-webkit-scrollbar-thumb:vertical:hover {
    background: #3B3B3B
}

::-webkit-scrollbar-corner:vertical {
    background: #535353
}

::-webkit-scrollbar-resizer:vertical {
    background: #FF6E00
}


input[type="text"]:focus, input[type="password"]:focus {
    border: none;
    outline: none;
}


.header, .mainBody, .footer {
    width: 960px;
    margin: 0 auto;
}


/*头部区域*/
.header {
    height: 90px;
    margin-bottom: 20px;
    background: url(../images/logo.png) no-repeat 0 center;
    border-bottom: 1px solid #d4d4d4;
}


/*主要区域*/
.mainBody {
    /*overflow-x: hidden;*/
    /*overflow-y: auto;*/
    padding: 0 10px;
}

.mainBody .correct, .mainBody .error {
    padding-left: 22px;
}

.mainBody .correct {
    background: url(../images/correctBg.png) no-repeat 0 center;
    color: green;
}

.mainBody .error {
    background: url(../images/errorBg.png) no-repeat 0 center;
    color: red;
}

.mainBody .text {
    line-height: 25px;
}

.mainBody .text h3 {
    font-size: 14px;
    text-align: center;
    margin-bottom: 10px;
    color: #333;
}

.mainBody .text h4 {
    font-size: 13px;
    margin: 10px 0 5px 0;
    color: #333;
}

.mainBody .text #install {
    height: 500px;
    overflow-x: hidden;
    overflow-y: auto;
}

.mainBody .text .successMsg {
    color: green;
}

.mainBody .text .errorMsg {
    color: red;
}

.mainBody .forms .head {
    font-weight: bold;
    font-size: 14px;
}

.mainBody .forms .firstCol {
    padding-left: 5px;
}

.mainBody .forms .endCol {
    padding-right: 5px;
    text-align: right;
}

.mainBody .table .title {
    font-weight: bold;
    font-size: 14px;
}

.mainBody .table .input, .readonly {
    width: 280px;
    height: 24px;
    line-height: 24px;
    margin: 1px;
    border: 1px solid #ccc;
    padding: 0 3px;
}

.mainBody .table .readonly {
    display: inline-block;
    border: none;
}

.mainBody .table .inputOn {
    width: 280px;
    height: 24px;
    line-height: 24px;
    padding: 2px 5px;
    background: url(../images/inputOnBg.png) no-repeat 0 center;
    border: 0;
}

.mainBody .table .cnote {
    margin-left: 20px;
    color: #999;
}

.mainBody .note {
    width: 400px;
    margin: 80px auto 0;
}

.mainBody .note .complete, .mainBody .note .already {
    width: 400px;
    padding: 90px 0 0 95px;
    line-height: 22px;
}

.mainBody .note .complete {
    background: url(../images/complete.png) no-repeat;
}

.mainBody .note .already {
    background: url(../images/already.png) no-repeat;
}

.mainBody .note span {
    color: #999;
    padding: 0 5px;
}

.mainBody .note a {
    text-decoration: underline;
}

.mainBody .note a:hover {
    text-decoration: none;
}

.mainBody .note a.link {
    text-decoration: none;
}

.mainBody .note a.link:hover {
    text-decoration: underline;
}


/* 页脚区域 */
.footer {
    margin-top: 20px;
    height: 50px;
    border-top: 3px solid #f0f0f0;
    background: #fff;
    position: relative;
}

.footer .step, .footer .step2, .footer .step3, .footer .step4, .footer .step5 {
    display: block;
    height: 3px;
    position: absolute;
    left: 0;
    bottom: 50px;
    background: #0cd7f7;
    overflow: hidden;
    z-index: 999;
}

.footer .step {
    width: 25%;
}

.footer .step2 {
    width: 50%;
}

.footer .step3 {
    width: 75%;
}

.footer .step4 {
    width: 100%;
}

.footer .step5 {
    width: 100%;
}

.footer .copyright {
    float: left;
    padding-left: 10px;
    line-height: 50px;
    font-family: Verdana;
}

.footer .formSubBtn {
    width: 200px;
    float: right;
    margin-top: 10px;
    padding-right: 20px;
    text-align: right;
}

.footer .formSubBtn .submit, .formSubBtn .back {
    display: inline-block;
    width: 78px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    font-family: "微软雅黑";
    font-size: 13px;
}

.footer .formSubBtn .submit {
    background: #3d566d;
    border: 1px solid #3d566d;
    color: #fff;
}

.footer .formSubBtn .submit:hover {
    background: #4f6880;
    text-decoration: none;
}

.footer .formSubBtn .back {
    margin-right: 10px;
    background: #fff;
    border: 1px solid #999;
    color: #666;
}

.footer .formSubBtn .back:hover {
    border: 1px solid #666;
    text-decoration: none;
}