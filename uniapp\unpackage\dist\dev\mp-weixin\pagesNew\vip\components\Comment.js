"use strict";
const api_comment = require("../../../api/comment.js");
const common_vendor = require("../../../common/vendor.js");
const AvatarImage = () => "../../../components/avatar-image/index.js";
const _sfc_main = {
  components: {
    AvatarImage
  },
  props: {
    // 商品ID
    goodsId: {
      type: Number,
      default: null
    },
    // 加载多少条记录 默认2条
    limit: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 评星数据转换
      rates: { 10: 5, 20: 3, 30: 1 },
      // 评价列表数据
      list: [],
      // 评价总数量
      total: 0
    };
  },
  created() {
    this.getCommentList();
  },
  methods: {
    // 加载评价列表数据
    getCommentList() {
      const app = this;
      app.isLoading = true;
      api_comment.listRows(app.goodsId, app.limit).then((result) => {
        app.list = result.data.list;
        app.total = result.data.total;
      }).catch((err) => err).finally(() => app.isLoading = false);
    },
    // 跳转到评论列表页
    onTargetToComment() {
      const app = this;
      app.$navTo("pages/comment/index", { goodsId: app.goodsId });
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _easycom_u_rate2 = common_vendor.resolveComponent("u-rate");
  (_component_avatar_image + _easycom_u_rate2)();
}
const _easycom_u_rate = () => "../../../uni_modules/vk-uview-ui/components/u-rate/u-rate.js";
if (!Math) {
  _easycom_u_rate();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading && $data.list.length
  }, !$data.isLoading && $data.list.length ? {
    b: common_vendor.t($data.total),
    c: common_vendor.o((...args) => $options.onTargetToComment && $options.onTargetToComment(...args)),
    d: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: "30b3f719-0-" + i0,
        b: common_vendor.p({
          url: item.user.avatar_url,
          width: 50
        }),
        c: common_vendor.t(item.user.nick_name),
        d: "30b3f719-1-" + i0,
        e: common_vendor.p({
          ["active-color"]: "#f4a213",
          current: $data.rates[item.score],
          disabled: true
        }),
        f: common_vendor.t(item.content),
        g: common_vendor.t(item.create_time),
        h: index
      };
    })
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-30b3f719"]]);
wx.createComponent(Component);
