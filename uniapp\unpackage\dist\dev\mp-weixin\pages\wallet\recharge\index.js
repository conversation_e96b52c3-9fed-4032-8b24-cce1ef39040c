"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_recharge = require("../../../api/recharge.js");
const common_enum_payment_Method = require("../../../common/enum/payment/Method.js");
const utils_util = require("../../../utils/util.js");
const core_payment_alipay = require("../../../core/payment/alipay.js");
const core_payment_wechat = require("../../../core/payment/wechat.js");
const PayMethodIconEnum = {
  [common_enum_payment_Method.PayMethodEnum.WECHAT.value]: "icon-wechat-pay",
  [common_enum_payment_Method.PayMethodEnum.ALIPAY.value]: "icon-alipay"
};
const PayMethodClientNameEnum = {
  [common_enum_payment_Method.PayMethodEnum.WECHAT.value]: "微信",
  [common_enum_payment_Method.PayMethodEnum.ALIPAY.value]: "支付宝"
};
const _sfc_main = {
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 按钮禁用
      disabled: false,
      // 枚举类
      PayMethodEnum: common_enum_payment_Method.PayMethodEnum,
      PayMethodIconEnum,
      PayMethodClientNameEnum,
      // 个人信息
      personal: { balance: "0.00" },
      // 充值设置
      setting: {},
      // 充值方案列表
      planList: [],
      // 当前客户端的支付方式列表（后端根据platform判断）
      methods: [],
      // 当前选中的套餐ID
      selectedPlanId: 0,
      // 自定义金额
      inputValue: "",
      // 当前选中的支付方式
      curPaymentItem: null,
      // 支付确认弹窗
      showConfirmModal: false
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getPageData();
  },
  methods: {
    // 选择充值套餐
    onSelectPlan(planId) {
      this.selectedPlanId = planId;
      this.inputValue = "";
    },
    // 金额输入框
    onChangeMoney({ detail }) {
      this.inputValue = detail.value;
      this.selectedPlanId = 0;
    },
    // 选择支付方式
    handleSelectPayType(index) {
      this.curPaymentItem = this.methods[index];
    },
    // 获取页面数据
    getPageData() {
      const app = this;
      app.isLoading = true;
      return new Promise((resolve, reject) => {
        api_recharge.center({ client: app.platform }).then((result) => {
          app.setting = result.data.setting;
          app.personal = result.data.personal;
          app.planList = result.data.planList;
          app.methods = result.data.paymentMethods;
          app.isLoading = false;
          app.setDefaultPayType();
        });
      });
    },
    // 默认选中的支付方式
    setDefaultPayType() {
      if (!this.curPaymentItem) {
        this.handleSelectPayType(0);
      }
    },
    // 判断当前页面来源于浏览器返回并提示手动查单
    // 立即充值
    onSubmit(e) {
      const app = this;
      if (!app.curPaymentItem) {
        app.$toast("您还没有选择支付方式");
        return;
      }
      if (app.disabled)
        return;
      app.disabled = true;
      api_recharge.submit({
        planId: app.selectedPlanId,
        customMoney: app.inputValue ? app.inputValue : "",
        method: app.curPaymentItem.method,
        client: app.platform,
        extra: app.getExtraAsUnify(app.curPaymentItem.method)
      }).then((result) => app.onSubmitCallback(result)).finally((err) => {
        setTimeout(() => app.disabled = false, 10);
      });
    },
    // 获取第三方支付的扩展参数
    getExtraAsUnify(method) {
      if (method === common_enum_payment_Method.PayMethodEnum.ALIPAY.value) {
        return core_payment_alipay.extraAsUnify();
      }
      if (method === common_enum_payment_Method.PayMethodEnum.WECHAT.value) {
        return core_payment_wechat.extraAsUnify();
      }
      return {};
    },
    // 订单提交成功后回调
    onSubmitCallback(result) {
      const app = this;
      const method = app.curPaymentItem.method;
      const paymentData = result.data.payment;
      if (method === common_enum_payment_Method.PayMethodEnum.BALANCE.value) {
        app.onShowSuccess(result);
      }
      if (method === common_enum_payment_Method.PayMethodEnum.ALIPAY.value) {
        console.log("paymentData", paymentData);
        core_payment_alipay.payment({ orderKey: "recharge", ...paymentData }).then((res) => app.onPaySuccess(res)).catch((err) => app.onPayFail(err));
      }
      if (method === common_enum_payment_Method.PayMethodEnum.WECHAT.value) {
        console.log("paymentData", paymentData);
        core_payment_wechat.payment({ orderKey: "recharge", ...paymentData }).then((res) => app.onPaySuccess(res)).catch((err) => app.onPayFail(err));
      }
    },
    // 订单支付成功的回调方法
    // 这里只是前端支付api返回结果success,实际订单是否支付成功 以后端的查单和异步通知为准
    onPaySuccess({ res, option: { isRequireQuery, outTradeNo, method } }) {
      const app = this;
      if (isRequireQuery) {
        app.onTradeQuery(outTradeNo, method);
        return true;
      }
      this.onShowSuccess(res);
    },
    // 显示支付成功信息并页面跳转
    onShowSuccess({ message }) {
      this.$toast(message || "订单支付成功");
      this.onSuccessNav();
    },
    // 订单支付失败
    onPayFail(err) {
      console.log("onPayFail", err);
      const errMsg = err.message || "订单未支付";
      this.$error(errMsg);
    },
    // 已完成支付按钮事件: 请求后端查单
    onTradeQuery(outTradeNo, method) {
      const app = this;
      api_recharge.tradeQuery({ outTradeNo, method, client: app.platform }).then((result) => result.data.isPay ? app.onShowSuccess(result) : app.onPayFail(result)).finally(() => app.showConfirmModal = false);
    },
    // 支付成功后的跳转
    onSuccessNav() {
      const pages = getCurrentPages();
      const lastPage = pages.length < 2 ? null : pages[pages.length - 2];
      const backRoutes = ["pages/wallet/index"];
      if (lastPage && utils_util.inArray(lastPage.route, backRoutes)) {
        setTimeout(() => common_vendor.index.navigateBack(), 1e3);
      } else {
        setTimeout(() => {
          this.$navTo("pages/wallet/index", {}, "redirectTo");
        }, 1200);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.personal.user_id
  }, $data.personal.user_id ? common_vendor.e({
    b: common_vendor.t($data.personal.balance),
    c: common_vendor.f($data.planList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.money),
        b: common_vendor.t(item.gift_money),
        c: $data.selectedPlanId == item.plan_id ? 1 : "",
        d: common_vendor.o(($event) => $options.onSelectPlan(item.plan_id), index),
        e: index
      };
    }),
    d: $data.setting.is_custom == 1
  }, $data.setting.is_custom == 1 ? {
    e: common_vendor.o([($event) => $data.inputValue = $event.detail.value, (...args) => $options.onChangeMoney && $options.onChangeMoney(...args)]),
    f: $data.inputValue
  } : {}, {
    g: common_vendor.f($data.methods, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n($data.PayMethodIconEnum[item.method]),
        b: common_vendor.n(item.method),
        c: common_vendor.t($data.PayMethodEnum[item.method].name),
        d: $data.curPaymentItem && $data.curPaymentItem.method == item.method
      }, $data.curPaymentItem && $data.curPaymentItem.method == item.method ? {} : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.handleSelectPayType(index), index)
      });
    }),
    h: $data.disabled,
    i: common_vendor.o((...args) => $options.onSubmit && $options.onSubmit(...args)),
    j: common_vendor.t($data.setting.describe),
    k: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-26c9e6d7"]]);
wx.createPage(MiniProgramPage);
