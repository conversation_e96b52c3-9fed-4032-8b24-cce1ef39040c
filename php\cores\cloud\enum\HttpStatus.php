<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores\cloud\enum;

/**
 * HTTP返回状态枚举类
 * Class StatusEnum
 * @package cores\cloud
 */
final class HttpStatus
{
    // 请求成功
    const SUCCESS = 200;

    // 未登录
    const NOT_LOGGED = 401;

    // 没有权限访问
    const NOT_PERMISSION = 403;

    // 服务器内部错误
    const ERROR = 500;
}

