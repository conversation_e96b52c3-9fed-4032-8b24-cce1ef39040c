"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const api_captcha = require("../../../api/captcha.js");
const utils_verify = require("../../../utils/verify.js");
const MpWeixinMobile = () => "./mp-weixin-mobile.js";
const times = 60;
const GET_CAPTCHA = 10;
const SUBMIT_LOGIN = 20;
const _sfc_main = {
  components: {
    MpWeixinMobile
  },
  props: {
    // 是否存在第三方用户信息
    isParty: {
      type: Boolean,
      default: () => false
    },
    // 第三方用户信息数据
    partyData: {
      type: Object
    },
    // 是否显示微信小程序端 一键授权手机号
    isMpWeixinMobile: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      // 正在加载
      isLoading: false,
      // 按钮禁用
      disabled: false,
      // 图形验证码信息
      captcha: {},
      // 短信验证码发送状态
      smsState: false,
      // 倒计时
      times,
      // 手机号
      mobile: "",
      // 图形验证码
      captchaCode: "",
      // 短信验证码
      smsCode: ""
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  created() {
    this.getCaptcha();
  },
  methods: {
    // 获取图形验证码
    getCaptcha() {
      const app = this;
      api_captcha.image().then((result) => app.captcha = result.data);
    },
    // 点击发送短信验证码
    handelSmsCaptcha() {
      const app = this;
      if (!app.isLoading && !app.smsState && app.formValidation(GET_CAPTCHA)) {
        app.sendSmsCaptcha();
      }
    },
    // 表单验证
    formValidation(scene = GET_CAPTCHA) {
      const app = this;
      if (scene === GET_CAPTCHA) {
        if (!app.validteMobile(app.mobile) || !app.validteCaptchaCode(app.captchaCode)) {
          return false;
        }
      }
      if (scene === SUBMIT_LOGIN) {
        if (!app.validteMobile(app.mobile) || !app.validteSmsCode(app.smsCode)) {
          return false;
        }
      }
      return true;
    },
    // 验证手机号
    validteMobile(str) {
      if (utils_verify.isEmpty(str)) {
        this.$toast("请先输入手机号");
        return false;
      }
      if (!utils_verify.isMobile(str)) {
        this.$toast("请输入正确格式的手机号");
        return false;
      }
      return true;
    },
    // 验证图形验证码
    validteCaptchaCode(str) {
      if (utils_verify.isEmpty(str)) {
        this.$toast("请先输入图形验证码");
        return false;
      }
      return true;
    },
    // 验证短信验证码
    validteSmsCode(str) {
      if (utils_verify.isEmpty(str)) {
        this.$toast("请先输入短信验证码");
        return false;
      }
      return true;
    },
    // 请求发送短信验证码接口
    sendSmsCaptcha() {
      const app = this;
      app.isLoading = true;
      api_captcha.sendSmsCaptcha({
        form: {
          captchaKey: app.captcha.key,
          captchaCode: app.captchaCode,
          mobile: app.mobile
        }
      }).then((result) => {
        app.$toast(result.message);
        app.timer();
      }).catch(() => app.getCaptcha()).finally(() => app.isLoading = false);
    },
    // 执行定时器
    timer() {
      const app = this;
      app.smsState = true;
      const inter = setInterval(() => {
        app.times = app.times - 1;
        if (app.times <= 0) {
          app.smsState = false;
          app.times = times;
          clearInterval(inter);
        }
      }, 1e3);
    },
    // 点击登录
    handleLogin() {
      const app = this;
      if (!app.isLoading && !app.disabled && app.formValidation(SUBMIT_LOGIN)) {
        app.submitLogin();
      }
    },
    // 确认登录
    submitLogin() {
      const app = this;
      app.isLoading = true;
      app.disabled = true;
      store_index.store.dispatch("Login", {
        smsCode: app.smsCode,
        mobile: app.mobile,
        isParty: app.isParty,
        partyData: app.partyData,
        refereeId: store_index.store.getters.refereeId
      }).then((result) => {
        app.$toast(result.message);
        common_vendor.index.$emit("syncRefresh", true);
        setTimeout(() => app.onNavigateBack(1), 2e3);
      }).catch((err) => {
        app.disabled = false;
        if (err.result.data.isBack) {
          setTimeout(() => app.onNavigateBack(1), 2e3);
        }
      }).finally(() => app.isLoading = false);
    },
    /**
     * 登录成功-跳转回原页面
     */
    onNavigateBack(delta = 1) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: Number(delta || 1)
        });
      } else {
        this.$navTo("pages/index/index");
      }
    }
  }
};
if (!Array) {
  const _component_MpWeixinMobile = common_vendor.resolveComponent("MpWeixinMobile");
  _component_MpWeixinMobile();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.isMpWeixinMobile
  }, $props.isMpWeixinMobile ? {
    b: common_vendor.p({
      isParty: $props.isParty,
      partyData: $props.partyData
    })
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b0b53126"]]);
wx.createComponent(Component);
