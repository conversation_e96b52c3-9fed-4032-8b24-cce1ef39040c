"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "dealer.withdraw/list",
  submit: "dealer.withdraw/submit",
  submitBalance: "dealer.withdraw/submitBalance"
};
const submitBalance = (data) => {
  return utils_request_index.$http.post(api.submitBalance, data);
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
const submit = (data) => {
  return utils_request_index.$http.post(api.submit, data);
};
exports.list = list;
exports.submit = submit;
exports.submitBalance = submitBalance;
