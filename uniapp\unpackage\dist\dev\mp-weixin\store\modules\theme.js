"use strict";
const utils_storage = require("../../utils/storage.js");
const utils_util = require("../../utils/util.js");
const store_mutationTypes = require("../mutation-types.js");
const theme = {
  state: {
    // 当前自定义主题
    appTheme: {
      mainBg: "#fa2209",
      mainBg2: "#ff6335",
      mainText: "#ffffff",
      viceBg: "#ffb100",
      viceBg2: "#ffb900",
      viceText: "#ffffff"
    }
  },
  mutations: {
    SET_APP_THEME: (state, value) => {
      if (!utils_util.isEmpty(value)) {
        state.appTheme = value;
      }
    }
  },
  actions: {
    // 记录自定义主题
    SetAppTheme({ commit }, value) {
      return new Promise((resolve, reject) => {
        utils_storage.storage.set(store_mutationTypes.APP_THEME, value);
        commit("SET_APP_THEME", value);
        resolve();
      });
    }
  }
};
exports.theme = theme;
