"use strict";
require("../common/vendor.js");
const formatDate = (time) => {
  return time.replace(/\-/g, "/");
};
const urlEncode = (obj = {}) => {
  const result = [];
  for (const key in obj) {
    const item = obj[key];
    if (!item) {
      continue;
    }
    if (isArray(item)) {
      item.forEach((val) => {
        result.push(key + "=" + val);
      });
    } else {
      result.push(key + "=" + item);
    }
  }
  return result.join("&");
};
const urlDecode = (queryStr = "") => {
  var newObj = new Object();
  if (queryStr) {
    var strs = queryStr.split("&");
    for (var i = 0; i < strs.length; i++) {
      newObj[strs[i].split("=")[0]] = strs[i].split("=")[1] || "";
    }
  }
  return newObj;
};
const inArray = (search, array) => {
  for (var i in array) {
    if (array[i] == search)
      return true;
  }
  return false;
};
const isEmptyObject = (object) => {
  return Object.keys(object).length === 0;
};
const isObject = (object) => {
  return Object.prototype.toString.call(object) === "[object Object]";
};
const isArray = (array) => {
  return Object.prototype.toString.call(array) === "[object Array]";
};
const isEmpty = (value) => {
  if (isArray(value)) {
    return value.length === 0;
  }
  if (isObject(value)) {
    return isEmptyObject(value);
  }
  return !value;
};
const hasOwnProperty = (object, key) => {
  return isObject(object) && object[key] !== void 0;
};
const cloneObj = (obj) => {
  let newObj = isArray(obj) ? [] : {};
  if (typeof obj !== "object") {
    return;
  }
  for (let i in obj) {
    newObj[i] = typeof obj[i] === "object" ? cloneObj(obj[i]) : obj[i];
  }
  return newObj;
};
function debounce(fn, delay = 100) {
  let timer;
  return function() {
    const that = this;
    const _args = arguments;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(function() {
      fn.apply(that, _args);
    }, delay);
  };
}
const arrayIntersect = (array1, array2) => {
  return array1.filter((val) => array2.indexOf(val) > -1);
};
exports.arrayIntersect = arrayIntersect;
exports.cloneObj = cloneObj;
exports.debounce = debounce;
exports.formatDate = formatDate;
exports.hasOwnProperty = hasOwnProperty;
exports.inArray = inArray;
exports.isArray = isArray;
exports.isEmpty = isEmpty;
exports.isEmptyObject = isEmptyObject;
exports.isObject = isObject;
exports.urlDecode = urlDecode;
exports.urlEncode = urlEncode;
