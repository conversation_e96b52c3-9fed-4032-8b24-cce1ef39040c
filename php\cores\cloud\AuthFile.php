<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores\cloud;

use cores\exception\BaseException;

/**
 * auth文件操作
 * Class AuthFile
 * @package cores\cloud
 */
class AuthFile
{
    // auth文件内容
    private static array $data = [];

    /**
     * 读取auth文件数据
     * @return array
     * @throws BaseException
     */
    public static function getData(): array
    {
        if (empty(self::$data)) {
            $file = app()->getRootPath() . 'auth';
            !is_readable($file) && throwError('授权文件不可读，请检查根目录下auth文件');
            $data = call_user_func(function () use ($file) {
                return include $file;
            });
            if (empty($data) || !is_array($data)) {
                throwError('授权文件数据格式不正确，请检查根目录下auth文件');
            }
            self::$data = $data;
        }
        return self::$data;
    }

    /**
     * 获取AppID
     * @return mixed
     * @throws BaseException
     */
    public static function getAppID()
    {
        return self::getData()['AppID'];
    }

    /**
     * 获取UserKey
     * @return mixed
     * @throws BaseException
     */
    public static function getUserKey()
    {
        return self::getData()['UserKey'];
    }
}