"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const common_model_Setting = require("../../common/model/Setting.js");
const common_enum_setting_Key = require("../../common/enum/setting/Key.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 会员信息
      userInfo: {},
      // 充值设置
      setting: {}
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getPageData();
  },
  methods: {
    tixian() {
      common_vendor.index.navigateTo({
        url: "/pages/wallet/apply"
      });
    },
    // 获取页面数据
    getPageData() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getUserInfo(), app.getSetting()]).then(() => app.isLoading = false);
    },
    // 获取会员信息
    getUserInfo() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_user.info().then((result) => {
          app.userInfo = result.data.userInfo;
          resolve(app.userInfo);
        });
      });
    },
    // 获取充值设置
    getSetting() {
      const app = this;
      return new Promise((resolve, reject) => {
        common_model_Setting.SettingModel.item(common_enum_setting_Key.SettingKeyEnum.RECHARGE.value, false).then((data) => {
          app.setting = data;
          resolve(data);
        });
      });
    },
    // 跳转充值页面
    onTargetRecharge() {
      this.$navTo("pages/wallet/recharge/index");
    },
    // 跳转充值记录页面
    onTargetRechargeOrder() {
      this.$navTo("pages/wallet/recharge/order");
    },
    // 跳转账单详情页面
    onTargetBalanceLog() {
      this.$navTo("pages/wallet/balance/log");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: common_assets._imports_0$1,
    c: common_vendor.t($data.userInfo.balance),
    d: $data.setting.is_entrance
  }, $data.setting.is_entrance ? {
    e: common_vendor.o(($event) => $options.onTargetRecharge())
  } : {}, {
    f: common_vendor.o(($event) => $options.tixian()),
    g: common_vendor.o(($event) => $options.onTargetRechargeOrder()),
    h: common_vendor.o(($event) => $options.onTargetBalanceLog())
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-70996a17"]]);
wx.createPage(MiniProgramPage);
