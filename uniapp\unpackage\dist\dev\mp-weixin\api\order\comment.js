"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "order.comment/list",
  submit: "order.comment/submit"
};
const list = (orderId, param) => {
  return utils_request_index.$http.get(api.list, { orderId, ...param });
};
const submit = (orderId, data) => {
  return utils_request_index.$http.post(api.submit, { orderId, form: data });
};
exports.list = list;
exports.submit = submit;
