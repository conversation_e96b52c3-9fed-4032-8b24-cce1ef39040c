"use strict";
const common_vendor = require("../../common/vendor.js");
const common_enum_order_refund_AuditStatus = require("../../common/enum/order/refund/AuditStatus.js");
const common_enum_order_refund_RefundStatus = require("../../common/enum/order/refund/RefundStatus.js");
const common_enum_order_refund_RefundType = require("../../common/enum/order/refund/RefundType.js");
const api_refund = require("../../api/refund.js");
const api_express = require("../../api/express.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      type: 0,
      // 枚举类
      AuditStatusEnum: common_enum_order_refund_AuditStatus.AuditStatusEnum,
      RefundStatusEnum: common_enum_order_refund_RefundStatus.RefundStatusEnum,
      RefundTypeEnum: common_enum_order_refund_RefundType.RefundTypeEnum,
      // 正在加载
      isLoading: true,
      // 售后单ID
      orderRefundId: null,
      // 售后单详情
      detail: {},
      // 物流公司列表
      expressList: [],
      // 表单数据
      formData: {
        // 物流公司ID
        expressId: null,
        // 物流单号
        expressNo: ""
      },
      // 选择的物流公司索引
      expressIndex: -1,
      // 按钮禁用
      disabled: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ orderRefundId, type }) {
    this.orderRefundId = orderRefundId;
    this.type = type;
    this.getPageData();
  },
  methods: {
    // 获取页面数据
    getPageData() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getRefundDetail(), app.getExpressList()]).then((result) => app.isLoading = false);
    },
    // 获取售后单详情
    getRefundDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_refund.detail(app.orderRefundId).then((result) => {
          app.detail = result.data.detail;
          resolve();
        }).catch(reject);
      });
    },
    // 获取物流公司列表
    getExpressList() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_express.list().then((result) => {
          app.expressList = result.data.list;
          resolve();
        }).catch(reject);
      });
    },
    // 跳转商品详情页
    onGoodsDetail(goodsId) {
      if (this.type == 2) {
        this.$navTo("pagesNew/points/detail", { goodsId });
      } else {
        this.$navTo("pages/goods/detail", { goodsId });
      }
    },
    // 凭证图片预览
    handlePreviewImages(index) {
      const { detail: { images } } = this;
      const imageUrls = images.map((item) => item.image_url);
      common_vendor.index.previewImage({
        current: imageUrls[index],
        urls: imageUrls
      });
    },
    // 选择物流公司
    onChangeExpress(e) {
      const expressIndex = e.detail.value;
      const { expressList } = this;
      this.expressIndex = expressIndex;
      this.formData.expressId = expressList[expressIndex].express_id;
    },
    // 表单提交
    onSubmit() {
      const app = this;
      if (app.disabled === true)
        return false;
      app.disabled = true;
      api_refund.delivery(app.orderRefundId, app.formData).then((result) => {
        app.$toast(result.message);
        setTimeout(() => {
          app.disabled = false;
          common_vendor.index.navigateBack();
        }, 1500);
      }).catch((err) => app.disabled = false);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: common_assets._imports_0$3,
    c: common_vendor.t($data.detail.state_text),
    d: $data.detail.orderGoods.goods_image,
    e: common_vendor.t($data.detail.orderGoods.goods_name),
    f: common_vendor.f($data.detail.orderGoods.goods_props, (props, idx, i0) => {
      return {
        a: common_vendor.t(props.value.name),
        b: idx
      };
    }),
    g: common_vendor.t($data.detail.orderGoods.total_num),
    h: common_vendor.o(($event) => $options.onGoodsDetail($data.detail.orderGoods.goods_id)),
    i: $data.type == 2
  }, $data.type == 2 ? {
    j: common_vendor.t(Number($data.detail.orderGoods.total_pay_price))
  } : {
    k: common_vendor.t($data.detail.orderGoods.total_pay_price)
  }, {
    l: $data.detail.status == $data.RefundStatusEnum.COMPLETED.value && $data.detail.type == 10
  }, $data.detail.status == $data.RefundStatusEnum.COMPLETED.value && $data.detail.type == 10 ? {
    m: common_vendor.t($data.detail.refund_money)
  } : {}, {
    n: $data.detail.status == $data.RefundStatusEnum.REJECTED.value
  }, $data.detail.status == $data.RefundStatusEnum.REJECTED.value ? common_vendor.e({
    o: common_vendor.t($data.RefundTypeEnum[$data.detail.type].name),
    p: common_vendor.t($data.detail.apply_desc),
    q: $data.detail.images.length > 0
  }, $data.detail.images.length > 0 ? {
    r: common_vendor.f($data.detail.images, (item, index, i0) => {
      return {
        a: item.image_url,
        b: common_vendor.o(($event) => $options.handlePreviewImages(index), index),
        c: index
      };
    })
  } : {}) : {}, {
    s: $data.detail.status.value == 10
  }, $data.detail.status.value == 10 ? {
    t: common_vendor.t($data.detail.refuse_desc)
  } : {}, {
    v: $data.detail.audit_status == $data.AuditStatusEnum.REVIEWED.value && $data.detail.is_user_send
  }, $data.detail.audit_status == $data.AuditStatusEnum.REVIEWED.value && $data.detail.is_user_send ? {
    w: common_vendor.t($data.detail.express.express_name),
    x: common_vendor.t($data.detail.express_no),
    y: common_vendor.t($data.detail.send_time)
  } : {}, {
    z: $data.detail.audit_status == $data.AuditStatusEnum.REVIEWED.value
  }, $data.detail.audit_status == $data.AuditStatusEnum.REVIEWED.value ? {
    A: common_vendor.t($data.detail.address.name),
    B: common_vendor.t($data.detail.address.phone),
    C: common_vendor.f($data.detail.address.region, (region, idx, i0) => {
      return {
        a: common_vendor.t(region),
        b: idx
      };
    }),
    D: common_vendor.t($data.detail.address.detail)
  } : {}, {
    E: $data.detail.type == $data.RefundTypeEnum.RETURN.value && $data.detail.audit_status == $data.AuditStatusEnum.REVIEWED.value && !$data.detail.is_user_send
  }, $data.detail.type == $data.RefundTypeEnum.RETURN.value && $data.detail.audit_status == $data.AuditStatusEnum.REVIEWED.value && !$data.detail.is_user_send ? common_vendor.e({
    F: $data.expressIndex > -1
  }, $data.expressIndex > -1 ? {
    G: common_vendor.t($data.expressList[$data.expressIndex].express_name)
  } : {}, {
    H: $data.expressList,
    I: $data.expressIndex,
    J: common_vendor.o((...args) => $options.onChangeExpress && $options.onChangeExpress(...args)),
    K: $data.formData.expressNo,
    L: common_vendor.o(($event) => $data.formData.expressNo = $event.detail.value),
    M: $data.disabled ? 1 : "",
    N: common_vendor.o(($event) => $options.onSubmit())
  }) : {}, {
    O: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ae660660"]]);
wx.createPage(MiniProgramPage);
