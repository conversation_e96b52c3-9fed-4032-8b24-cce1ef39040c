"use strict";
const defaultConfig = {
  // 系统名称
  name: "萤火商城2.0",
  /**
   * 后端api地址 (必填; 斜杠/结尾; 请确保能访问)
   * 例如: https://www.你的域名.com/index.php?s=/api/
   */
  apiUrl: "./index.php?s=/api/",
  /**
   * 商城ID (必填) 
   * 可在超管后台-商城列表中查看
   */
  storeId: 10001,
  /**
   * 是否启用商城设置缓存
   * 将减少用户端重复请求; 正式运营时请设为true, 开启后商城设置同步前端需10分钟缓存
   */
  enabledSettingCache: true,
  /**
   * 是否开启APP端的微信分享功能
   * 如果开启, 需配置manifest.json中 APP模块配置 -> Share(分享) -> 微信分享
   */
  enabledAppShareWeixin: false,
  /**
   * 是否启用H5端多开
   * 启用后将通过获取子域名中的ID作为storeId; 例如域名是 "shop10001.baidu.com", 那么storeId就是10001
   */
  enabledH5Multi: false,
  /**
   * 获取子域名ID的正则
   */
  domainIdRegex: /shop[\-]?(\d+)\./
};
exports.defaultConfig = defaultConfig;
