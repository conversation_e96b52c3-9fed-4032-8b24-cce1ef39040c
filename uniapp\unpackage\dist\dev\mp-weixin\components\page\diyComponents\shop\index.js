"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 跳转到门店详情页
    handleNavDetail(shopId) {
      this.$navTo("pages/shop/detail", { shopId });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (dataItem, index, i0) => {
      return {
        a: dataItem.logo_url,
        b: common_vendor.t(dataItem.shop_name),
        c: common_vendor.t(dataItem.region.province),
        d: common_vendor.t(dataItem.region.city),
        e: common_vendor.t(dataItem.region.region),
        f: common_vendor.t(dataItem.address),
        g: common_vendor.t(dataItem.phone),
        h: index,
        i: common_vendor.o(($event) => $options.handleNavDetail(dataItem.shop_id), index)
      };
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b6ea3413"]]);
wx.createComponent(Component);
