"use strict";
const common_model_Store = require("../Store.js");
require("../../vendor.js");
const data = () => {
  return new Promise((resolve, reject) => {
    common_model_Store.StoreModel.data().then((data2) => {
      resolve(data2.clientData.wxofficial.setting);
    });
  });
};
const item = (key) => {
  return new Promise((resolve, reject) => {
    data().then((setting) => resolve(setting[key]));
  });
};
const WxofficialSettingModel = {
  data,
  item
};
exports.WxofficialSettingModel = WxofficialSettingModel;
