"use strict";
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_recharge_order = require("../../../api/recharge/order.js");
const core_app = require("../../../core/app.js");
const common_vendor = require("../../../common/vendor.js");
const pageSize = 15;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 余额账单明细列表
      list: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于12条才显示无更多数据
        noMoreSize: 12,
        // 空布局
        empty: {
          tip: "亲，暂无充值记录"
        }
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getLogList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取余额账单明细列表
    getLogList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_recharge_order.list({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    }
  }
};
if (!Array) {
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  _easycom_mescroll_body2();
}
const _easycom_mescroll_body = () => "../../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.list.data, (item, index, i0) => {
      return {
        a: common_vendor.t(item.pay_time),
        b: common_vendor.t(item.actual_money),
        c: index
      };
    }),
    b: common_vendor.t("充值成功"),
    c: common_vendor.sr("mescrollRef", "9576ccee-0"),
    d: common_vendor.o(_ctx.mescrollInit),
    e: common_vendor.o($options.upCallback),
    f: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9576ccee"]]);
wx.createPage(MiniProgramPage);
