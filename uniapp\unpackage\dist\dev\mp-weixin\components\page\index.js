"use strict";
const common_vendor = require("../../common/vendor.js");
const Search = () => "./diyComponents/search/index.js";
const Images = () => "./diyComponents/image/index.js";
const Banner = () => "./diyComponents/banner/index.js";
const Window = () => "./diyComponents/window/index.js";
const HotZone = () => "./diyComponents/hotZone/index.js";
const Videos = () => "./diyComponents/video/index.js";
const Article = () => "./diyComponents/article/index.js";
const Notice = () => "./diyComponents/notice/index.js";
const NavBar = () => "./diyComponents/navBar/index.js";
const Goods = () => "./diyComponents/goods/index.js";
const Service = () => "./diyComponents/service/index.js";
const Blank = () => "./diyComponents/blank/index.js";
const Guide = () => "./diyComponents/guide/index.js";
const RichText = () => "./diyComponents/richText/index.js";
const Special = () => "./diyComponents/special/index.js";
const DiyOfficialAccount = () => "./diyComponents/officialAccount/index.js";
const Shop = () => "./diyComponents/shop/index.js";
const Coupon = () => "./diyComponents/coupon/index.js";
const Bargain = () => "./diyComponents/bargain/index.js";
const Sharp = () => "./diyComponents/sharp/index.js";
const Groupon = () => "./diyComponents/groupon/index.js";
const ICPLicense = () => "./diyComponents/ICPLicense/index.js";
const Title = () => "./diyComponents/Title/index.js";
const GoodsGroup = () => "./diyComponents/goodsGroup/index.js";
const _sfc_main = {
  components: {
    Search,
    Images,
    Banner,
    Window,
    HotZone,
    Videos,
    Article,
    Notice,
    NavBar,
    Goods,
    Service,
    Blank,
    Guide,
    RichText,
    Special,
    DiyOfficialAccount,
    Shop,
    Coupon,
    Bargain,
    Sharp,
    Groupon,
    ICPLicense,
    Title,
    GoodsGroup
  },
  props: {
    items: {
      type: Array,
      default() {
        return [];
      }
    }
  }
};
if (!Array) {
  const _component_Search = common_vendor.resolveComponent("Search");
  const _component_Images = common_vendor.resolveComponent("Images");
  const _component_Banner = common_vendor.resolveComponent("Banner");
  const _component_Window = common_vendor.resolveComponent("Window");
  const _component_Videos = common_vendor.resolveComponent("Videos");
  const _component_Article = common_vendor.resolveComponent("Article");
  const _component_Notice = common_vendor.resolveComponent("Notice");
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _component_Goods = common_vendor.resolveComponent("Goods");
  const _component_Service = common_vendor.resolveComponent("Service");
  const _component_Blank = common_vendor.resolveComponent("Blank");
  const _component_Guide = common_vendor.resolveComponent("Guide");
  const _component_RichText = common_vendor.resolveComponent("RichText");
  const _component_Special = common_vendor.resolveComponent("Special");
  const _component_DiyOfficialAccount = common_vendor.resolveComponent("DiyOfficialAccount");
  const _component_Shop = common_vendor.resolveComponent("Shop");
  const _component_Coupon = common_vendor.resolveComponent("Coupon");
  const _component_Bargain = common_vendor.resolveComponent("Bargain");
  const _component_Sharp = common_vendor.resolveComponent("Sharp");
  const _component_Groupon = common_vendor.resolveComponent("Groupon");
  const _component_HotZone = common_vendor.resolveComponent("HotZone");
  const _component_ICPLicense = common_vendor.resolveComponent("ICPLicense");
  const _component_Title = common_vendor.resolveComponent("Title");
  const _component_GoodsGroup = common_vendor.resolveComponent("GoodsGroup");
  (_component_Search + _component_Images + _component_Banner + _component_Window + _component_Videos + _component_Article + _component_Notice + _component_NavBar + _component_Goods + _component_Service + _component_Blank + _component_Guide + _component_RichText + _component_Special + _component_DiyOfficialAccount + _component_Shop + _component_Coupon + _component_Bargain + _component_Sharp + _component_Groupon + _component_HotZone + _component_ICPLicense + _component_Title + _component_GoodsGroup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.items, (item, index, i0) => {
      return common_vendor.e({
        a: item.type === "search"
      }, item.type === "search" ? {
        b: "766a8132-0-" + i0,
        c: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        d: item.type === "image"
      }, item.type === "image" ? {
        e: "766a8132-1-" + i0,
        f: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        g: item.type === "banner"
      }, item.type === "banner" ? {
        h: "766a8132-2-" + i0,
        i: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        j: item.type === "window"
      }, item.type === "window" ? {
        k: "766a8132-3-" + i0,
        l: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        m: item.type === "video"
      }, item.type === "video" ? {
        n: "766a8132-4-" + i0,
        o: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        p: item.type === "article"
      }, item.type === "article" ? {
        q: "766a8132-5-" + i0,
        r: common_vendor.p({
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        s: item.type === "notice"
      }, item.type === "notice" ? {
        t: "766a8132-6-" + i0,
        v: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        w: item.type === "navBar"
      }, item.type === "navBar" ? {
        x: "766a8132-7-" + i0,
        y: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        z: item.type === "goods"
      }, item.type === "goods" ? {
        A: "766a8132-8-" + i0,
        B: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        C: item.type === "service"
      }, item.type === "service" ? {
        D: "766a8132-9-" + i0,
        E: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        F: item.type === "blank"
      }, item.type === "blank" ? {
        G: "766a8132-10-" + i0,
        H: common_vendor.p({
          itemStyle: item.style
        })
      } : {}, {
        I: item.type === "guide"
      }, item.type === "guide" ? {
        J: "766a8132-11-" + i0,
        K: common_vendor.p({
          itemStyle: item.style
        })
      } : {}, {
        L: item.type === "richText"
      }, item.type === "richText" ? {
        M: "766a8132-12-" + i0,
        N: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        O: item.type === "special"
      }, item.type === "special" ? {
        P: "766a8132-13-" + i0,
        Q: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        R: item.type === "officialAccount"
      }, item.type === "officialAccount" ? {
        S: "766a8132-14-" + i0
      } : {}, {
        T: item.type === "shop"
      }, item.type === "shop" ? {
        U: "766a8132-15-" + i0,
        V: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        W: item.type === "coupon"
      }, item.type === "coupon" ? {
        X: "766a8132-16-" + i0,
        Y: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        Z: item.type === "bargain"
      }, item.type === "bargain" ? {
        aa: "766a8132-17-" + i0,
        ab: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        ac: item.type === "sharp"
      }, item.type === "sharp" ? {
        ad: "766a8132-18-" + i0,
        ae: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          data: item.data
        })
      } : {}, {
        af: item.type === "groupon"
      }, item.type === "groupon" ? {
        ag: "766a8132-19-" + i0,
        ah: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        ai: item.type === "hotZone"
      }, item.type === "hotZone" ? {
        aj: "766a8132-20-" + i0,
        ak: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          data: item.data
        })
      } : {}, {
        al: item.type === "ICPLicense"
      }, item.type === "ICPLicense" ? {
        am: "766a8132-21-" + i0,
        an: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        ao: item.type === "title"
      }, item.type === "title" ? {
        ap: "766a8132-22-" + i0,
        aq: common_vendor.p({
          itemStyle: item.style,
          params: item.params
        })
      } : {}, {
        ar: item.type === "goodsGroup"
      }, item.type === "goodsGroup" ? {
        as: "766a8132-23-" + i0,
        at: common_vendor.p({
          itemStyle: item.style,
          params: item.params,
          dataList: item.data
        })
      } : {}, {
        av: index
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
