"use strict";
const store_index = require("../../store/index.js");
const api_store = require("../../api/store.js");
const utils_storage = require("../../utils/storage.js");
const common_model_Setting = require("./Setting.js");
require("../vendor.js");
const CACHE_KEY = "Store";
const setStorage = (data2) => {
  const expireTime = 10 * 60;
  utils_storage.storage.set(CACHE_KEY, data2, expireTime);
};
const getStorage = () => {
  return utils_storage.storage.get(CACHE_KEY);
};
const getApiData = () => {
  return new Promise((resolve, reject) => {
    api_store.StoreApi.data().then((result) => {
      initStoreData(result.data);
      resolve(result.data);
    });
  });
};
const initStoreData = (data2) => {
  setStorage(data2);
  common_model_Setting.SettingModel.setStorage(data2.setting);
  common_model_Setting.SettingModel.setAppTheme();
  store_index.store.dispatch("SetModules", data2.modules);
};
const data = (isCache = true) => {
  return new Promise((resolve, reject) => {
    const cacheData = getStorage();
    if (isCache && cacheData) {
      resolve(cacheData);
    } else {
      getApiData().then((data2) => {
        resolve(data2);
      });
    }
  });
};
const storeInfo = () => {
  return new Promise((resolve, reject) => {
    data().then((data2) => resolve(data2.storeInfo));
  });
};
const h5Url = () => {
  return new Promise((resolve, reject) => {
    data().then((data2) => {
      const h5Url2 = data2.clientData.h5.setting.baseUrl;
      resolve(h5Url2);
    });
  });
};
const isEnabledDealer = () => {
  return new Promise((resolve, reject) => {
    data().then((data2) => {
      const isEnabledDealer2 = Boolean(data2.dealer.setting.is_open);
      resolve(isEnabledDealer2);
    });
  });
};
const StoreModel = {
  data,
  storeInfo,
  h5Url,
  isEnabledDealer
};
exports.StoreModel = StoreModel;
