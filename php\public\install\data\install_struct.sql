DROP TABLE IF EXISTS `yoshop_admin_user`;
CREATE TABLE `yoshop_admin_user` (
  `admin_user_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_name` varchar(255) NOT NULL DEFAULT '' COMMENT '用户名',
  `password` varchar(255) NOT NULL DEFAULT '' COMMENT '登录密码',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`admin_user_id`),
  KEY `user_name` (`user_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='超管用户记录表';

DROP TABLE IF EXISTS `yoshop_article`;
CREATE TABLE `yoshop_article` (
  `article_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(300) NOT NULL DEFAULT '' COMMENT '文章标题',
  `show_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '列表显示方式(10小图展示 20大图展示)',
  `category_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文章分类ID',
  `image_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '封面图ID',
  `content` longtext NOT NULL COMMENT '文章内容',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文章排序(数字越小越靠前)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '文章状态(0隐藏 1显示)',
  `virtual_views` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '虚拟阅读量(仅用作展示)',
  `actual_views` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '实际阅读量',
  `is_delete` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`article_id`),
  KEY `category_id` (`category_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章记录表';

DROP TABLE IF EXISTS `yoshop_article_category`;
CREATE TABLE `yoshop_article_category` (
  `category_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章分类ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态(1显示 0隐藏)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序方式(数字越小越靠前)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章分类表';

DROP TABLE IF EXISTS `yoshop_bargain_active`;
CREATE TABLE `yoshop_bargain_active` (
  `active_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '砍价活动ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `expiryt_time` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '砍价有效期(单位：小时)',
  `floor_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '砍价底价',
  `peoples` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '帮砍人数',
  `is_self_cut` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '可自砍一刀(0禁止 1允许)',
  `is_floor_buy` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '必须底价购买(0否 1是)',
  `is_restrict` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否开启限购(0未开启 1已开启)',
  `restrict_total` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '总限购数量(0为不限制)',
  `share_title` varchar(500) NOT NULL DEFAULT '' COMMENT '分享标题',
  `prompt_words` varchar(500) NOT NULL DEFAULT '' COMMENT '砍价助力语',
  `actual_sales` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动销量(实际的)',
  `initial_sales` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '虚拟销量',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '活动状态(1进行中 0已结束)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`active_id`),
  KEY `goods_id` (`goods_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='砍价活动表';

DROP TABLE IF EXISTS `yoshop_bargain_setting`;
CREATE TABLE `yoshop_bargain_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='砍价活动设置表';

DROP TABLE IF EXISTS `yoshop_bargain_task`;
CREATE TABLE `yoshop_bargain_task` (
  `task_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '砍价任务ID',
  `active_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '砍价活动ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id(发起人)',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `goods_sku_id` varchar(150) NOT NULL DEFAULT '' COMMENT '商品sku标识',
  `goods_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品原价',
  `floor_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '砍价底价',
  `peoples` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '帮砍人数',
  `cut_people` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已砍人数',
  `section` text NOT NULL COMMENT '砍价金额区间',
  `cut_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '已砍金额',
  `actual_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际购买金额',
  `is_floor` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否已砍到底价(0否 1是)',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务截止时间',
  `is_buy` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否购买(0未购买 1已购买)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '任务状态 (0已结束 1进行中)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  KEY `active_id` (`active_id`),
  KEY `user_id` (`user_id`),
  KEY `goods_id` (`goods_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='砍价任务表';

DROP TABLE IF EXISTS `yoshop_bargain_task_help`;
CREATE TABLE `yoshop_bargain_task_help` (
  `help_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `active_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '砍价活动ID',
  `task_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '砍价任务ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `is_creater` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否为发起人(0否 1是)',
  `cut_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '砍掉的金额',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`help_id`),
  KEY `active_id` (`active_id`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='砍价任务助力记录表';

DROP TABLE IF EXISTS `yoshop_cart`;
CREATE TABLE `yoshop_cart` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `goods_sku_id` varchar(150) NOT NULL COMMENT '商品sku唯一标识',
  `goods_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品数量',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `goods_id` (`goods_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`),
  KEY `goods_id_2` (`goods_id`,`goods_sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车记录表';

DROP TABLE IF EXISTS `yoshop_category`;
CREATE TABLE `yoshop_category` (
  `category_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品分类ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级分类ID',
  `image_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类图片ID',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(1显示 0隐藏)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序方式(数字越小越靠前)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

DROP TABLE IF EXISTS `yoshop_comment`;
CREATE TABLE `yoshop_comment` (
  `comment_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `score` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '评分 (10好评 20中评 30差评)',
  `content` text NOT NULL COMMENT '评价内容',
  `is_picture` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否为图片评价',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(0隐藏 1显示)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '评价排序',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `order_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单商品ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `is_delete` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`comment_id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `goods_id` (`goods_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品评价记录表';

DROP TABLE IF EXISTS `yoshop_comment_image`;
CREATE TABLE `yoshop_comment_image` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '评价ID',
  `image_id` int(11) NOT NULL DEFAULT '0' COMMENT '图片id(关联文件记录表)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `comment_id` (`comment_id`) USING BTREE,
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品评价图片记录表';

DROP TABLE IF EXISTS `yoshop_coupon`;
CREATE TABLE `yoshop_coupon` (
  `coupon_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `coupon_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '优惠券类型(10满减券 20折扣券)',
  `reduce_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '满减券-减免金额',
  `discount` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '折扣券-折扣率(0-100)',
  `min_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '最低消费金额',
  `expire_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '到期类型(10领取后生效 20固定时间)',
  `expire_day` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '领取后生效-有效天数',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '固定时间-开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '固定时间-结束时间',
  `apply_range` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '适用范围(10全部商品 20指定商品 30排除商品)',
  `apply_range_config` text COMMENT '适用范围配置(json格式)',
  `total_num` int(11) NOT NULL DEFAULT '0' COMMENT '发放总数量(-1为不限制)',
  `receive_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已领取数量',
  `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '优惠券描述',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(1显示 0隐藏)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序方式(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '软删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`coupon_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券记录表';

DROP TABLE IF EXISTS `yoshop_dealer_apply`;
CREATE TABLE `yoshop_dealer_apply` (
  `apply_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `real_name` varchar(30) NOT NULL DEFAULT '' COMMENT '姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `referee_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '推荐人用户ID',
  `apply_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '申请方式(10需后台审核 20无需审核)',
  `apply_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请时间',
  `apply_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '审核状态 (10待审核 20审核通过 30驳回)',
  `audit_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '审核时间',
  `reject_reason` varchar(500) NOT NULL DEFAULT '' COMMENT '驳回原因',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`apply_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商申请记录表';

DROP TABLE IF EXISTS `yoshop_dealer_capital`;
CREATE TABLE `yoshop_dealer_capital` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户ID',
  `flow_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '资金流动类型 (10佣金收入 20提现支出)',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '描述',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商资金明细表';

DROP TABLE IF EXISTS `yoshop_dealer_order`;
CREATE TABLE `yoshop_dealer_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '买家用户ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '订单总金额(不含运费)',
  `first_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户id(一级)',
  `second_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户id(二级)',
  `third_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户id(三级)',
  `first_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(一级)',
  `second_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(二级)',
  `third_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(三级)',
  `is_invalid` tinyint(3) NOT NULL DEFAULT '0' COMMENT '订单是否失效(0未失效 1已失效)',
  `is_settled` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '佣金结算(0未结算 1已结算)',
  `settle_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '结算时间',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `first_user_id` (`first_user_id`),
  KEY `second_user_id` (`second_user_id`),
  KEY `third_user_id` (`third_user_id`),
  KEY `is_settled` (`is_settled`),
  KEY `is_invalid` (`is_invalid`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商订单记录表';

DROP TABLE IF EXISTS `yoshop_dealer_referee`;
CREATE TABLE `yoshop_dealer_referee` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dealer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id(被推荐人)',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '推荐关系层级(1,2,3)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `dealer_id` (`dealer_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商推荐关系表';

DROP TABLE IF EXISTS `yoshop_dealer_setting`;
CREATE TABLE `yoshop_dealer_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商设置表';

DROP TABLE IF EXISTS `yoshop_dealer_user`;
CREATE TABLE `yoshop_dealer_user` (
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户ID',
  `real_name` varchar(30) NOT NULL DEFAULT '' COMMENT '姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '当前可提现佣金',
  `freeze_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '已冻结佣金',
  `total_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累积提现佣金',
  `referee_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '推荐人用户ID',
  `first_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '成员数量(一级)',
  `second_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '成员数量(二级)',
  `third_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '成员数量(三级)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商用户记录表';

DROP TABLE IF EXISTS `yoshop_dealer_withdraw`;
CREATE TABLE `yoshop_dealer_withdraw` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分销商用户ID',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `pay_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '打款方式 (10微信 20支付宝 30银行卡)',
  `alipay_name` varchar(30) NOT NULL DEFAULT '' COMMENT '支付宝姓名',
  `alipay_account` varchar(30) NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `bank_name` varchar(30) NOT NULL DEFAULT '' COMMENT '开户行名称',
  `bank_account` varchar(30) NOT NULL DEFAULT '' COMMENT '银行开户名',
  `bank_card` varchar(30) NOT NULL DEFAULT '' COMMENT '银行卡号',
  `apply_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '申请状态 (10待审核 20审核通过 30驳回 40已打款)',
  `audit_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '审核时间',
  `reject_reason` varchar(500) NOT NULL DEFAULT '' COMMENT '驳回原因',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '来源客户端(APP、H5、小程序等)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商提现明细表';

DROP TABLE IF EXISTS `yoshop_delivery`;
CREATE TABLE `yoshop_delivery` (
  `delivery_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '模板名称',
  `method` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '计费方式(10按件数 20按重量)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序方式(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '小程序d',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`delivery_id`,`is_delete`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送模板主表';

DROP TABLE IF EXISTS `yoshop_delivery_rule`;
CREATE TABLE `yoshop_delivery_rule` (
  `rule_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `delivery_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '配送模板ID',
  `region` text NOT NULL COMMENT '可配送区域(城市id集)',
  `region_text` text NOT NULL COMMENT '可配送区域(文字展示)',
  `first` double unsigned NOT NULL DEFAULT '0' COMMENT '首件(个)/首重(Kg)',
  `first_fee` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '运费(元)',
  `additional` double unsigned NOT NULL DEFAULT '0' COMMENT '续件/续重',
  `additional_fee` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '续费(元)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`rule_id`),
  KEY `delivery_id` (`delivery_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送模板区域及运费表';

DROP TABLE IF EXISTS `yoshop_eorder_setting`;
CREATE TABLE `yoshop_eorder_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电子面单设置表';

DROP TABLE IF EXISTS `yoshop_eorder_template`;
CREATE TABLE `yoshop_eorder_template` (
  `template_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '面单模板ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '面单模板名称',
  `provider` varchar(30) NOT NULL DEFAULT '' COMMENT 'API网关(快递100、快递鸟)',
  `express_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物流公司ID',
  `config` text NOT NULL COMMENT '模板配置(json格式)',
  `remarks` varchar(500) NOT NULL DEFAULT '' COMMENT '管理员备注',
  `sort` int(3) unsigned NOT NULL DEFAULT '100' COMMENT '排序(数字越小越靠前)',
  `is_delete` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电子面单模板记录表';

DROP TABLE IF EXISTS `yoshop_express`;
CREATE TABLE `yoshop_express` (
  `express_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '物流公司ID',
  `express_name` varchar(255) NOT NULL DEFAULT '' COMMENT '物流公司名称',
  `kuaidi100_code` varchar(30) NOT NULL DEFAULT '' COMMENT '物流公司编码 (快递100)',
  `kdniao_code` varchar(30) NOT NULL DEFAULT '' COMMENT '物流公司编码 (快递鸟)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`express_id`),
  KEY `store_id` (`store_id`),
  KEY `kuaidi100_code` (`kuaidi100_code`) USING BTREE,
  KEY `express_name` (`express_name`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流公司记录表';

DROP TABLE IF EXISTS `yoshop_goods`;
CREATE TABLE `yoshop_goods` (
  `goods_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `goods_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '商品类型（10实物商品 20虚拟商品）',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_no` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
  `video_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '主图视频ID',
  `video_cover_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '主图视频ID',
  `selling_point` varchar(500) NOT NULL DEFAULT '' COMMENT '商品卖点',
  `spec_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '商品规格(10单规格 20多规格)',
  `goods_price_min` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品价格(最低)',
  `goods_price_max` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品价格(最高)',
  `line_price_min` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '划线价格(最低)',
  `line_price_max` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '划线价格(最高)',
  `stock_total` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '库存总量(包含所有sku)',
  `deduct_stock_type` tinyint(3) unsigned NOT NULL DEFAULT '20' COMMENT '库存计算方式(10下单减库存 20付款减库存)',
  `is_restrict` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否开启限购(0未开启 1已开启)',
  `restrict_total` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '总限购数量(0为不限制)',
  `restrict_single` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '每单限购数量(0为不限制)',
  `content` longtext NOT NULL COMMENT '商品详情',
  `sales_initial` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '初始销量',
  `sales_actual` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '实际销量',
  `delivery_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '配送模板ID',
  `is_points_gift` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否开启积分赠送(1开启 0关闭)',
  `is_points_discount` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否允许使用积分抵扣(1允许 0不允许)',
  `is_alone_points_discount` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '积分抵扣设置(0默认抵扣 1单独设置抵扣)',
  `points_discount_config` varchar(500) NOT NULL DEFAULT '' COMMENT '单独设置积分抵扣的配置',
  `is_enable_grade` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否开启会员折扣(1开启 0关闭)',
  `is_alone_grade` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '会员折扣设置(0默认等级折扣 1单独设置折扣)',
  `alone_grade_equity` text COMMENT '单独设置折扣的配置',
  `is_ind_dealer` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否开启单独分销(0关闭 1开启)',
  `dealer_money_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '分销佣金类型(10百分比 20固定金额)',
  `first_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(一级)',
  `second_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(二级)',
  `third_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(三级)',
  `is_ind_delivery_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否单独设置配送方式(0关闭 1开启)',
  `delivery_type` varchar(255) NOT NULL DEFAULT '' COMMENT '商品配送方式(仅单独设置时有效)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '商品状态(10上架 20下架)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`goods_id`),
  KEY `goods_no` (`goods_no`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品记录表';

DROP TABLE IF EXISTS `yoshop_goods_category_rel`;
CREATE TABLE `yoshop_goods_category_rel` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `category_id` int(11) unsigned NOT NULL COMMENT '商品分类ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`),
  KEY `goods_id` (`goods_id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品与分类关系记录表';

DROP TABLE IF EXISTS `yoshop_goods_image`;
CREATE TABLE `yoshop_goods_image` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `image_id` int(11) NOT NULL COMMENT '图片id(关联文件记录表)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `goods_id` (`goods_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品图片记录表';

DROP TABLE IF EXISTS `yoshop_goods_import`;
CREATE TABLE `yoshop_goods_import` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `total_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '导入总数量',
  `success_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '导入成功数量',
  `fail_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '导入失败数量',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `fail_log` text NOT NULL COMMENT '导入失败日志',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '导入状态(10进行中 20已完成)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品批量导入记录表';

DROP TABLE IF EXISTS `yoshop_goods_service`;
CREATE TABLE `yoshop_goods_service` (
  `service_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品服务ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '服务名称',
  `summary` varchar(500) NOT NULL DEFAULT '' COMMENT '概述',
  `is_default` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否默认(新增商品时)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(1显示 0隐藏)',
  `sort` int(11) unsigned NOT NULL DEFAULT '100' COMMENT '排序方式(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除(1已删除)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`service_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品服务与承诺记录表';

DROP TABLE IF EXISTS `yoshop_goods_service_rel`;
CREATE TABLE `yoshop_goods_service_rel` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `service_id` int(11) unsigned NOT NULL COMMENT '服务承诺ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`),
  KEY `goods_id` (`goods_id`),
  KEY `service_id` (`service_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品与服务承诺关系记录表';

DROP TABLE IF EXISTS `yoshop_goods_sku`;
CREATE TABLE `yoshop_goods_sku` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `goods_sku_id` varchar(150) NOT NULL DEFAULT '0' COMMENT '商品sku唯一标识 (由规格id组成)',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `image_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '规格图片ID',
  `goods_sku_no` varchar(100) NOT NULL DEFAULT '' COMMENT '商品sku编码',
  `goods_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品价格',
  `line_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品划线价',
  `stock_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '当前库存数量',
  `goods_weight` double unsigned NOT NULL DEFAULT '0' COMMENT '商品重量(Kg)',
  `goods_props` varchar(500) NOT NULL DEFAULT '' COMMENT 'SKU的规格属性(json格式)',
  `spec_value_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '规格值ID集(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku_idx` (`goods_id`,`goods_sku_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格表';

DROP TABLE IF EXISTS `yoshop_goods_spec_rel`;
CREATE TABLE `yoshop_goods_spec_rel` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `spec_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '规格组ID',
  `spec_value_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '规格值ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品与规格值关系记录表';

DROP TABLE IF EXISTS `yoshop_groupon_goods`;
CREATE TABLE `yoshop_groupon_goods` (
  `groupon_goods_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '拼团商品ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '主商品ID',
  `active_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '拼团类型(10普通拼团 20老带新拼团 30阶梯拼团)',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `spec_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '商品规格(10单规格 20多规格)',
  `groupon_price_min` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '拼团价格(最低)',
  `groupon_price_max` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '拼团价格(最高)',
  `is_alone_buy` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '开启单独购买(0未开启 1已开启)',
  `people` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '成团人数(固定人数)',
  `steps_config` text COMMENT '阶梯配置json(仅阶梯团)',
  `group_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '成团有效时间(单位:小时)',
  `is_quick_join` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '开启凑团(0未开启 1已开启)',
  `is_restrict` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '开启限购(0未开启 1已开启)',
  `restrict_total` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '总限购数量(0为不限制)',
  `restrict_single` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '每单限购数量(0为不限制)',
  `is_mock_task` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '开启模拟成团(0未开启 1已开启)',
  `mock_min_people` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '模拟成团最低参团人数',
  `initial_sales` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '初始销量',
  `actual_sales` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '实际销量',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品排序',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '活动状态(10进行中 20已结束)',
  `group_task_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '成团拼单数',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`groupon_goods_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`),
  KEY `goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团商品记录表';

DROP TABLE IF EXISTS `yoshop_groupon_goods_sku`;
CREATE TABLE `yoshop_groupon_goods_sku` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_sku_id` varchar(150) NOT NULL DEFAULT '0' COMMENT '商品sku记录索引 (由规格ID组成)',
  `groupon_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '拼团商品ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '主商品ID',
  `groupon_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '拼团价格 (普通团和老带新团)',
  `steps_price_config` text COMMENT '拼团价格 (阶梯团)',
  `goods_props` varchar(500) NOT NULL DEFAULT '' COMMENT 'SKU的规格属性(json格式)',
  `spec_value_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '规格值ID集(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku_idx` (`groupon_goods_id`,`goods_sku_id`),
  KEY `goods_sku_id` (`goods_sku_id`) USING BTREE,
  KEY `store_id` (`store_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团商品sku信息表';

DROP TABLE IF EXISTS `yoshop_groupon_robot`;
CREATE TABLE `yoshop_groupon_robot` (
  `robot_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '机器人ID',
  `nick_name` varchar(255) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '头像文件ID',
  `gender` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '性别',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字小靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`robot_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团机器人记录表';

DROP TABLE IF EXISTS `yoshop_groupon_setting`;
CREATE TABLE `yoshop_groupon_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多人拼团设置表';

DROP TABLE IF EXISTS `yoshop_groupon_task`;
CREATE TABLE `yoshop_groupon_task` (
  `task_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '拼单ID',
  `groupon_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '拼团商品ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '主商品ID',
  `active_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '拼团类型(10普通拼团 20老带新拼团 30阶梯拼团)',
  `people` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '成团人数',
  `actual_people` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已拼人数(实际的)',
  `robot_people` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已拼人数(机器人)',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '团长用户ID(发起人)',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '拼单开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '拼单结束时间',
  `complete_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '完成时间',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '拼单状态(10进行中 20拼单成功 30拼单失败)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  KEY `groupon_goods_id` (`groupon_goods_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团拼单任务表';

DROP TABLE IF EXISTS `yoshop_groupon_task_users`;
CREATE TABLE `yoshop_groupon_task_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '拼单ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `is_leader` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否为团长(发起人)',
  `is_robot` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否为机器人',
  `robot_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团拼单成员记录表';

DROP TABLE IF EXISTS `yoshop_h5_setting`;
CREATE TABLE `yoshop_h5_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='H5端设置表';

DROP TABLE IF EXISTS `yoshop_help`;
CREATE TABLE `yoshop_help` (
  `help_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '帮助标题',
  `content` text NOT NULL COMMENT '帮助内容',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除(1已删除)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`help_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助中心记录表';

DROP TABLE IF EXISTS `yoshop_order`;
CREATE TABLE `yoshop_order` (
  `order_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(20) NOT NULL DEFAULT '' COMMENT '订单号',
  `order_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '订单类型（10实物订单 20虚拟订单）',
  `total_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品总金额(不含优惠折扣)',
  `order_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '订单金额(含优惠折扣)',
  `coupon_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券ID',
  `coupon_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '优惠券抵扣金额',
  `points_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分抵扣金额',
  `points_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '积分抵扣数量',
  `pay_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际付款金额(包含运费)',
  `update_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '后台修改的订单金额(差价)',
  `buyer_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '买家留言',
  `pay_method` varchar(20) NOT NULL DEFAULT '' COMMENT '支付方式（余额/微信/支付宝）',
  `pay_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '付款状态(10未付款 20已付款)',
  `pay_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '付款时间',
  `trade_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '第三方交易记录ID',
  `delivery_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '配送方式(10快递配送 20门店自提 30无需配送)',
  `extract_shop_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '自提门店ID',
  `extract_clerk_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '核销店员ID',
  `express_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '运费金额',
  `delivery_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '发货状态(10未发货 20已发货 30部分发货)',
  `delivery_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发货时间',
  `receipt_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '收货状态(10未收货 20已收货)',
  `receipt_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '收货时间',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '订单状态(10进行中 20取消 21待取消 30已完成)',
  `points_bonus` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '赠送的积分数量',
  `merchant_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '商家备注',
  `is_settled` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单是否已结算(0未结算 1已结算)',
  `is_comment` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '是否已评价(0否 1是)',
  `order_source` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '订单来源(10普通订单 20砍价订单 30秒杀订单)',
  `order_source_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '来源记录ID',
  `order_source_data` varchar(500) NOT NULL DEFAULT '' COMMENT '来源记录的参数 (json格式)',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '来源客户端 (APP、H5、小程序等)',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `pay_status` (`pay_status`),
  KEY `delivery_status` (`delivery_status`),
  KEY `receipt_status` (`receipt_status`),
  KEY `order_status` (`order_status`),
  KEY `is_settled` (`is_settled`),
  KEY `order_source` (`order_source`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单记录表';

DROP TABLE IF EXISTS `yoshop_order_address`;
CREATE TABLE `yoshop_order_address` (
  `order_address_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `province_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '省份ID',
  `city_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '城市ID',
  `region_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '区/县ID',
  `detail` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`order_address_id`) USING BTREE,
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单收货地址记录表';

DROP TABLE IF EXISTS `yoshop_order_delivery`;
CREATE TABLE `yoshop_order_delivery` (
  `delivery_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '发货单ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `delivery_method` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '发货方式(10手动录入 20无需物流 30电子面单)',
  `express_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物流公司ID',
  `express_no` varchar(50) NOT NULL DEFAULT '' COMMENT '物流单号',
  `eorder_html` longtext NOT NULL COMMENT '电子面单模板内容',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`delivery_id`),
  KEY `order_id` (`order_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单发货单记录表';

DROP TABLE IF EXISTS `yoshop_order_delivery_goods`;
CREATE TABLE `yoshop_order_delivery_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `delivery_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发货单ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单商品ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `delivery_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发货数量',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `delivery_id` (`delivery_id`),
  KEY `order_id` (`order_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单发货单商品记录表';

DROP TABLE IF EXISTS `yoshop_order_export`;
CREATE TABLE `yoshop_order_export` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下单时间(开始)',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下单时间(结束)',
  `file_path` varchar(255) NOT NULL DEFAULT '' COMMENT 'excel文件路径',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '导出状态(10进行中 20已完成 30失败)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单导出Excel记录表';

DROP TABLE IF EXISTS `yoshop_order_extract`;
CREATE TABLE `yoshop_order_extract` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `linkman` varchar(30) NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自提订单联系方式记录表';

DROP TABLE IF EXISTS `yoshop_order_goods`;
CREATE TABLE `yoshop_order_goods` (
  `order_goods_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `goods_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '商品类型（10实物商品 20虚拟商品）',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `image_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品封面图ID',
  `deduct_stock_type` tinyint(3) unsigned NOT NULL DEFAULT '20' COMMENT '库存计算方式(10下单减库存 20付款减库存)',
  `spec_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '规格类型(10单规格 20多规格)',
  `goods_sku_id` varchar(150) NOT NULL DEFAULT '' COMMENT '商品sku唯一标识',
  `goods_props` varchar(255) NOT NULL DEFAULT '' COMMENT 'SKU的规格属性(json格式)',
  `content` longtext NOT NULL COMMENT '商品详情',
  `goods_no` varchar(100) NOT NULL DEFAULT '' COMMENT '商品编码',
  `goods_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品价格(单价)',
  `line_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品划线价',
  `goods_weight` double unsigned NOT NULL DEFAULT '0' COMMENT '商品重量(Kg)',
  `is_user_grade` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否存在会员等级折扣',
  `grade_ratio` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '会员折扣比例(0-10)',
  `grade_goods_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '会员折扣的商品单价',
  `grade_total_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '会员折扣的总额差',
  `coupon_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '优惠券折扣金额',
  `points_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分金额',
  `points_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '积分抵扣数量',
  `points_bonus` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '赠送的积分数量',
  `total_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '购买数量',
  `total_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品总价(数量×单价)',
  `total_pay_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际付款价(折扣和优惠后)',
  `delivery_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '发货状态(10未发货 20已发货 30部分发货)',
  `delivery_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已发货数量',
  `is_ind_dealer` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否开启单独分销(0关闭 1开启)',
  `dealer_money_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '分销佣金类型(10百分比 20固定金额)',
  `first_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(一级)',
  `second_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(二级)',
  `third_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '分销佣金(三级)',
  `is_comment` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '是否已评价(0否 1是)',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `goods_source_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '来源记录ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`order_goods_id`) USING BTREE,
  KEY `goods_id` (`goods_id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品记录表';

DROP TABLE IF EXISTS `yoshop_order_refund`;
CREATE TABLE `yoshop_order_refund` (
  `order_refund_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '售后单ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单商品ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '售后类型(10退货退款 20换货)',
  `apply_desc` varchar(1000) NOT NULL DEFAULT '' COMMENT '用户申请原因(说明)',
  `audit_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '商家审核状态(0待审核 10已同意 20已拒绝)',
  `refuse_desc` varchar(1000) NOT NULL DEFAULT '' COMMENT '商家拒绝原因(说明)',
  `refund_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际退款金额',
  `is_user_send` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '用户是否发货(0未发货 1已发货)',
  `send_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户发货时间',
  `express_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户发货物流公司ID',
  `express_no` varchar(32) NOT NULL DEFAULT '' COMMENT '用户发货物流单号',
  `is_receipt` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '商家收货状态(0未收货 1已收货)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '售后单状态(0进行中 10已拒绝 20已完成 30已取消)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_refund_id`),
  KEY `order_id` (`order_id`),
  KEY `order_goods_id` (`order_goods_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后单记录表';

DROP TABLE IF EXISTS `yoshop_order_refund_address`;
CREATE TABLE `yoshop_order_refund_address` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_refund_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '售后单ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `province_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所在省份ID',
  `city_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所在城市ID',
  `region_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所在区/县ID',
  `detail` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后单退货地址记录表';

DROP TABLE IF EXISTS `yoshop_order_refund_image`;
CREATE TABLE `yoshop_order_refund_image` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_refund_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '售后单ID',
  `image_id` int(11) NOT NULL DEFAULT '0' COMMENT '图片id(关联文件记录表)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `order_refund_id` (`order_refund_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后单图片记录表';

DROP TABLE IF EXISTS `yoshop_page`;
CREATE TABLE `yoshop_page` (
  `page_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `page_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '页面类型(10首页 20自定义页)',
  `page_name` varchar(255) NOT NULL DEFAULT '' COMMENT '页面名称',
  `page_data` longtext NOT NULL COMMENT '页面数据',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`page_id`),
  KEY `store_id` (`store_id`),
  KEY `page_type` (`page_type`,`store_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺页面记录表';

DROP TABLE IF EXISTS `yoshop_payment`;
CREATE TABLE `yoshop_payment` (
  `payment_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '支付方式ID',
  `client` varchar(30) NOT NULL DEFAULT '' COMMENT '客户端(H5、小程序、APP)',
  `method` varchar(30) NOT NULL DEFAULT '' COMMENT '支付方式(微信、支付宝、余额)',
  `is_must_template` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否需要支付模板',
  `template_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '支付模板ID',
  `is_enable` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用支付项',
  `is_default` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否为默认支付方式',
  `others` text NOT NULL COMMENT '其他选项(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`payment_id`),
  KEY `store_id` (`store_id`),
  KEY `client` (`client`,`method`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城支付方式记录表';

DROP TABLE IF EXISTS `yoshop_payment_template`;
CREATE TABLE `yoshop_payment_template` (
  `template_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '支付模板ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '支付模板名称',
  `method` varchar(30) NOT NULL DEFAULT '' COMMENT '支付方式(微信、支付宝、余额)',
  `config` text NOT NULL COMMENT '支付配置(json格式)',
  `remarks` varchar(500) NOT NULL DEFAULT '' COMMENT '管理员备注',
  `wechatpay_serial` varchar(255) NOT NULL DEFAULT '' COMMENT '微信支付V3平台证书序号',
  `sort` int(3) unsigned NOT NULL DEFAULT '100' COMMENT '排序(数字越小越靠前)',
  `is_delete` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城支付模板记录表';

DROP TABLE IF EXISTS `yoshop_payment_trade`;
CREATE TABLE `yoshop_payment_trade` (
  `trade_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
  `out_trade_no` varchar(50) NOT NULL DEFAULT '' COMMENT '交易订单号(全局唯一)',
  `client` varchar(30) NOT NULL DEFAULT '' COMMENT '发起客户端(H5、小程序、APP)',
  `pay_method` varchar(30) NOT NULL DEFAULT '' COMMENT '支付方式(微信、支付宝)',
  `order_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '订单类型(10商城订单 100余额充值订单)',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID(商城订单、充值订单)',
  `order_no` varchar(30) NOT NULL DEFAULT '' COMMENT '商城订单号(商城订单、充值订单)',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下单用户ID',
  `trade_no` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方交易流水号',
  `prepay_id` varchar(50) NOT NULL DEFAULT '' COMMENT '预支付交易会话ID(微信支付)',
  `trade_state` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '交易状态',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`trade_id`),
  UNIQUE KEY `out_trade_no` (`out_trade_no`),
  KEY `client` (`client`,`pay_method`) USING BTREE,
  KEY `order_type` (`order_type`),
  KEY `user_id` (`user_id`),
  KEY `trade_status` (`trade_state`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方支付交易记录表';

DROP TABLE IF EXISTS `yoshop_printer`;
CREATE TABLE `yoshop_printer` (
  `printer_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '打印机ID',
  `printer_name` varchar(255) NOT NULL DEFAULT '' COMMENT '打印机名称',
  `printer_type` varchar(255) NOT NULL DEFAULT '' COMMENT '打印机类型',
  `printer_config` text NOT NULL COMMENT '打印机配置',
  `print_times` smallint(6) unsigned NOT NULL DEFAULT '0' COMMENT '打印联数(次数)',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序 (数字越小越靠前)',
  `is_delete` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`printer_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小票打印机记录表';

DROP TABLE IF EXISTS `yoshop_recharge_order`;
CREATE TABLE `yoshop_recharge_order` (
  `order_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(20) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `recharge_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '充值方式(10自定义金额 20套餐充值)',
  `plan_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '充值套餐ID',
  `pay_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '用户支付金额',
  `gift_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '赠送金额',
  `actual_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `pay_method` varchar(20) NOT NULL DEFAULT '' COMMENT '支付方式（微信/支付宝）',
  `pay_status` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '支付状态(10待支付 20已支付)',
  `pay_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '付款时间',
  `trade_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '第三方交易记录ID',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '来源客户端 (APP、H5、小程序等)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `plan_id` (`plan_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员充值订单表';

DROP TABLE IF EXISTS `yoshop_recharge_order_plan`;
CREATE TABLE `yoshop_recharge_order_plan` (
  `order_plan_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `plan_id` int(11) unsigned NOT NULL COMMENT '主键ID',
  `plan_name` varchar(255) NOT NULL DEFAULT '' COMMENT '方案名称',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `gift_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '赠送金额',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_plan_id`),
  KEY `order_id` (`order_id`),
  KEY `plan_id` (`plan_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员充值订单套餐快照表';

DROP TABLE IF EXISTS `yoshop_recharge_plan`;
CREATE TABLE `yoshop_recharge_plan` (
  `plan_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_name` varchar(255) NOT NULL DEFAULT '' COMMENT '套餐名称',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `gift_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '赠送金额',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`plan_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员充值套餐表';

DROP TABLE IF EXISTS `yoshop_region`;
CREATE TABLE `yoshop_region` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '区划信息ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '区划名称',
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `code` varchar(255) NOT NULL DEFAULT '' COMMENT '区划编码',
  `level` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '层级(1省级 2市级 3区/县级)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3705 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='省市区数据表';

DROP TABLE IF EXISTS `yoshop_sharp_active`;
CREATE TABLE `yoshop_sharp_active` (
  `active_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '活动会场ID',
  `active_date` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动日期',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '活动状态(0禁用 1启用)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`active_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='整点秒杀-活动会场表';

DROP TABLE IF EXISTS `yoshop_sharp_active_goods`;
CREATE TABLE `yoshop_sharp_active_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `active_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动会场ID',
  `active_time_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动场次ID',
  `sharp_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '秒杀商品ID',
  `sales_actual` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '实际销量',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `active_id` (`active_id`),
  KEY `active_time_id` (`active_time_id`),
  KEY `sharp_goods_id` (`sharp_goods_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='整点秒杀-活动会场与商品关联表';

DROP TABLE IF EXISTS `yoshop_sharp_active_time`;
CREATE TABLE `yoshop_sharp_active_time` (
  `active_time_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '场次ID',
  `active_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动会场ID',
  `active_time` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '场次时间(0点-23点)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '活动状态(0禁用 1启用)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`active_time_id`),
  KEY `active_id` (`active_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='整点秒杀-活动会场场次表';

DROP TABLE IF EXISTS `yoshop_sharp_goods`;
CREATE TABLE `yoshop_sharp_goods` (
  `sharp_goods_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '秒杀商品ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `spec_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '商品规格(10单规格 20多规格)',
  `seckill_price_min` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '秒杀价格(最低)',
  `seckill_price_max` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '秒杀价格(最高)',
  `deduct_stock_type` tinyint(3) unsigned DEFAULT '10' COMMENT '库存计算方式(仅支持下单减库存)',
  `limit_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '限购数量(0为不限制)',
  `seckill_stock` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品库存总量',
  `total_sales` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '累积销量',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品排序(数字越小越靠前)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '商品状态(0下架 1上架)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`sharp_goods_id`),
  KEY `goods_id` (`goods_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='整点秒杀-商品表';

DROP TABLE IF EXISTS `yoshop_sharp_goods_sku`;
CREATE TABLE `yoshop_sharp_goods_sku` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_sku_id` varchar(150) NOT NULL DEFAULT '0' COMMENT '商品sku记录索引 (由规格id组成)',
  `sharp_goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '秒杀商品ID',
  `seckill_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '秒杀价格',
  `seckill_stock` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '秒杀库存数量',
  `goods_props` varchar(500) NOT NULL DEFAULT '' COMMENT 'SKU的规格属性(json格式)',
  `spec_value_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '规格值ID集(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku_idx` (`sharp_goods_id`,`goods_sku_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='整点秒杀-秒杀商品sku信息表';

DROP TABLE IF EXISTS `yoshop_sharp_setting`;
CREATE TABLE `yoshop_sharp_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='整点秒杀设置表';

DROP TABLE IF EXISTS `yoshop_spec`;
CREATE TABLE `yoshop_spec` (
  `spec_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规格组ID',
  `spec_name` varchar(255) NOT NULL DEFAULT '' COMMENT '规格组名称',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`spec_id`),
  KEY `spec_name` (`spec_name`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格组记录表';

DROP TABLE IF EXISTS `yoshop_spec_value`;
CREATE TABLE `yoshop_spec_value` (
  `spec_value_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规格值ID',
  `spec_value` varchar(255) NOT NULL COMMENT '规格值',
  `spec_id` int(11) NOT NULL COMMENT '规格组ID',
  `store_id` int(11) NOT NULL COMMENT '商城ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`spec_value_id`),
  KEY `spec_value` (`spec_value`),
  KEY `spec_id` (`spec_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格值记录表';

DROP TABLE IF EXISTS `yoshop_store`;
CREATE TABLE `yoshop_store` (
  `store_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '商城ID',
  `store_name` varchar(50) NOT NULL DEFAULT '' COMMENT '商城名称',
  `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '商城简介',
  `logo_image_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城logo文件ID',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `is_recycle` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否回收',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家(商户)记录表';

DROP TABLE IF EXISTS `yoshop_store_address`;
CREATE TABLE `yoshop_store_address` (
  `address_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '地址类型(10发货地址 20退货地址)',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `province_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '省份ID',
  `city_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '城市ID',
  `region_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '区/县ID',
  `detail` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`address_id`),
  KEY `type` (`type`),
  KEY `store_id` (`store_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家地址记录表';

DROP TABLE IF EXISTS `yoshop_store_api`;
CREATE TABLE `yoshop_store_api` (
  `api_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '权限名称',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '权限url',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `sort` int(11) unsigned NOT NULL DEFAULT '100' COMMENT '排序(数字越小越靠前)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`api_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家后台api权限表';

DROP TABLE IF EXISTS `yoshop_store_menu`;
CREATE TABLE `yoshop_store_menu` (
  `menu_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `module` tinyint(3) NOT NULL DEFAULT '10' COMMENT '模块类型(10菜单 20操作)',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '菜单名称',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '菜单路径(唯一)',
  `action_mark` varchar(255) NOT NULL DEFAULT '' COMMENT '操作标识',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级菜单ID',
  `sort` int(11) unsigned NOT NULL DEFAULT '100' COMMENT '排序(数字越小越靠前)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家后台菜单记录表';

DROP TABLE IF EXISTS `yoshop_store_menu_api`;
CREATE TABLE `yoshop_store_menu_api` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `menu_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '菜单ID',
  `api_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户角色ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `menu_id` (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家后台用户角色与菜单权限关系表';

DROP TABLE IF EXISTS `yoshop_store_role`;
CREATE TABLE `yoshop_store_role` (
  `role_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL DEFAULT '' COMMENT '角色名称',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级角色ID',
  `sort` int(11) unsigned NOT NULL DEFAULT '100' COMMENT '排序(数字越小越靠前)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家用户角色表';

DROP TABLE IF EXISTS `yoshop_store_role_menu`;
CREATE TABLE `yoshop_store_role_menu` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户角色ID',
  `menu_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '菜单ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家后台用户角色与菜单权限关系表';

DROP TABLE IF EXISTS `yoshop_store_setting`;
CREATE TABLE `yoshop_store_setting` (
  `key` varchar(30) NOT NULL COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容（json格式）',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家设置记录表';

DROP TABLE IF EXISTS `yoshop_store_shop`;
CREATE TABLE `yoshop_store_shop` (
  `shop_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `shop_name` varchar(255) NOT NULL DEFAULT '' COMMENT '门店名称',
  `logo_image_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '门店logo图片ID',
  `linkman` varchar(20) NOT NULL DEFAULT '' COMMENT '联系人',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `shop_hours` varchar(255) NOT NULL DEFAULT '' COMMENT '营业时间',
  `province_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所在省份ID',
  `city_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所在城市ID',
  `region_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所在辖区ID',
  `address` varchar(100) NOT NULL DEFAULT '' COMMENT '详细地址',
  `longitude` varchar(50) NOT NULL DEFAULT '' COMMENT '门店坐标经度',
  `latitude` varchar(50) NOT NULL DEFAULT '' COMMENT '门店坐标纬度',
  `geohash` varchar(50) NOT NULL DEFAULT '' COMMENT 'geohash',
  `summary` varchar(1000) NOT NULL DEFAULT '0' COMMENT '门店简介',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '门店排序(数字越小越靠前)',
  `is_check` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否支持自提核销(0否 1支持)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '门店状态(0禁用 1启用)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`shop_id`),
  KEY `is_check` (`store_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家门店记录表';

DROP TABLE IF EXISTS `yoshop_store_shop_clerk`;
CREATE TABLE `yoshop_store_shop_clerk` (
  `clerk_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '店员ID',
  `shop_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所属门店ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `real_name` varchar(30) NOT NULL DEFAULT '' COMMENT '店员姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '状态(0禁用 1启用)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`clerk_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家门店店员表';

DROP TABLE IF EXISTS `yoshop_store_shop_order`;
CREATE TABLE `yoshop_store_shop_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `shop_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '门店ID',
  `clerk_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '核销员ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `shop_id` (`shop_id`),
  KEY `clerk_id` (`clerk_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家门店核销订单记录表';

DROP TABLE IF EXISTS `yoshop_store_user`;
CREATE TABLE `yoshop_store_user` (
  `store_user_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_name` varchar(255) NOT NULL DEFAULT '' COMMENT '用户名',
  `password` varchar(255) NOT NULL DEFAULT '' COMMENT '登录密码',
  `real_name` varchar(255) NOT NULL DEFAULT '' COMMENT '姓名',
  `is_super` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否为超级管理员',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `sort` int(11) unsigned NOT NULL DEFAULT '100' COMMENT '排序(数字越小越靠前)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`store_user_id`),
  KEY `user_name` (`user_name`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家用户记录表';

DROP TABLE IF EXISTS `yoshop_store_user_role`;
CREATE TABLE `yoshop_store_user_role` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '超管用户ID',
  `role_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `store_user_id` (`store_user_id`) USING BTREE,
  KEY `role_id` (`role_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家用户角色记录表';

DROP TABLE IF EXISTS `yoshop_system_process`;
CREATE TABLE `yoshop_system_process` (
  `key` varchar(20) NOT NULL DEFAULT '' COMMENT '进程标识',
  `name` varchar(255) NOT NULL,
  `last_working_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后运行时间',
  PRIMARY KEY (`key`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统进程记录';

DROP TABLE IF EXISTS `yoshop_upload_file`;
CREATE TABLE `yoshop_upload_file` (
  `file_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文件分组ID',
  `channel` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '上传来源(10商户后台 20用户端)',
  `storage` varchar(10) NOT NULL DEFAULT '' COMMENT '存储方式',
  `domain` varchar(255) NOT NULL DEFAULT '' COMMENT '存储域名',
  `file_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '文件类型(10图片 20附件 30视频)',
  `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名称(仅显示)',
  `file_path` varchar(255) NOT NULL DEFAULT '' COMMENT '文件路径',
  `file_size` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `file_ext` varchar(20) NOT NULL DEFAULT '' COMMENT '文件扩展名',
  `cover` varchar(255) NOT NULL DEFAULT '' COMMENT '文件封面',
  `uploader_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上传者用户ID',
  `is_recycle` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否在回收站',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`file_id`),
  KEY `group_id` (`group_id`),
  KEY `is_recycle` (`is_recycle`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件库记录表';

DROP TABLE IF EXISTS `yoshop_upload_group`;
CREATE TABLE `yoshop_upload_group` (
  `group_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '分组名称',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级分组ID',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序(数字越小越靠前)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`group_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件库分组记录表';

DROP TABLE IF EXISTS `yoshop_user`;
CREATE TABLE `yoshop_user` (
  `user_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `nick_name` varchar(255) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '头像文件ID',
  `gender` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '性别',
  `country` varchar(50) NOT NULL DEFAULT '' COMMENT '国家',
  `province` varchar(50) NOT NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) NOT NULL DEFAULT '' COMMENT '城市',
  `address_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '默认收货地址',
  `balance` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '用户可用余额',
  `points` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户可用积分',
  `pay_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '用户总支付的金额',
  `expend_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际消费的金额(不含退款)',
  `grade_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '会员等级ID',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '注册来源客户端 (APP、H5、小程序等)',
  `last_login_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后登录时间',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  KEY `mobile` (`mobile`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户记录表';

DROP TABLE IF EXISTS `yoshop_user_address`;
CREATE TABLE `yoshop_user_address` (
  `address_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `province_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '省份ID',
  `city_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '城市ID',
  `region_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '区/县ID',
  `detail` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`address_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收货地址表';

DROP TABLE IF EXISTS `yoshop_user_balance_log`;
CREATE TABLE `yoshop_user_balance_log` (
  `log_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `scene` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '余额变动场景(10用户充值 20用户消费 30管理员操作 40订单退款)',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变动金额',
  `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '描述/说明',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '管理员备注',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户余额变动明细表';

DROP TABLE IF EXISTS `yoshop_user_coupon`;
CREATE TABLE `yoshop_user_coupon` (
  `user_coupon_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `coupon_id` int(11) unsigned NOT NULL COMMENT '优惠券ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `coupon_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '优惠券类型(10满减券 20折扣券)',
  `reduce_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '满减券-减免金额',
  `discount` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '折扣券-折扣率(0-100)',
  `min_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '最低消费金额',
  `expire_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '到期类型(10领取后生效 20固定时间)',
  `expire_day` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '领取后生效-有效天数',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '有效期开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '有效期结束时间',
  `apply_range` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '适用范围(10全部商品 20指定商品)',
  `apply_range_config` text COMMENT '适用范围配置(json格式)',
  `is_expire` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否过期(0未过期 1已过期)',
  `is_use` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否已使用(0未使用 1已使用)',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`user_coupon_id`),
  KEY `coupon_id` (`coupon_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券记录表';

DROP TABLE IF EXISTS `yoshop_user_grade`;
CREATE TABLE `yoshop_user_grade` (
  `grade_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '等级名称',
  `weight` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '等级权重(1-9999)',
  `upgrade` text NOT NULL COMMENT '升级条件',
  `equity` text NOT NULL COMMENT '等级权益(折扣率0-100)',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(1启用 0禁用)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`grade_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员等级表';

DROP TABLE IF EXISTS `yoshop_user_grade_log`;
CREATE TABLE `yoshop_user_grade_log` (
  `log_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `old_grade_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '变更前的等级ID',
  `new_grade_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '变更后的等级ID',
  `change_type` tinyint(3) unsigned NOT NULL DEFAULT '10' COMMENT '变更类型(10后台管理员设置 20自动升级)',
  `remark` varchar(500) DEFAULT '' COMMENT '管理员备注',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员等级变更记录表';

DROP TABLE IF EXISTS `yoshop_user_oauth`;
CREATE TABLE `yoshop_user_oauth` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `oauth_type` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方登陆类型(MP-WEIXIN)',
  `oauth_id` varchar(100) NOT NULL DEFAULT '' COMMENT '第三方用户唯一标识 (uid openid)',
  `unionid` varchar(100) NOT NULL DEFAULT '' COMMENT '微信unionID',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `oauth_type` (`oauth_type`),
  KEY `store_id` (`store_id`),
  KEY `oauth_type_2` (`oauth_type`,`oauth_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方用户信息表';

DROP TABLE IF EXISTS `yoshop_user_points_log`;
CREATE TABLE `yoshop_user_points_log` (
  `log_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `value` int(11) NOT NULL DEFAULT '0' COMMENT '变动数量',
  `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '描述/说明',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '管理员备注',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分变动明细表';

DROP TABLE IF EXISTS `yoshop_wxapp_live_room`;
CREATE TABLE `yoshop_wxapp_live_room` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` int(11) unsigned NOT NULL COMMENT '直播间ID(来源微信)',
  `room_name` varchar(200) NOT NULL DEFAULT '' COMMENT '直播间名称',
  `cover_img` varchar(255) DEFAULT '' COMMENT '分享卡片封面',
  `share_img` varchar(255) DEFAULT '' COMMENT '直播间背景墙封面',
  `anchor_name` varchar(30) NOT NULL DEFAULT '' COMMENT '主播昵称',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '开播时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `live_status` tinyint(3) unsigned NOT NULL DEFAULT '102' COMMENT '直播状态(101: 直播中, 102: 未开始, 103: 已结束, 104: 禁播, 105: 暂停中, 106: 异常, 107: 已过期)',
  `is_top` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '置顶状态(0未置顶 1已置顶)',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除(0未删除 1已删除)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `room_id` (`room_id`),
  KEY `live_status` (`live_status`),
  KEY `is_top` (`is_top`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序直播间记录表';

DROP TABLE IF EXISTS `yoshop_wxapp_setting`;
CREATE TABLE `yoshop_wxapp_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序设置表';

DROP TABLE IF EXISTS `yoshop_wxofficial_setting`;
CREATE TABLE `yoshop_wxofficial_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号设置表';

DROP TABLE IF EXISTS `yoshop_goods_collector`;
CREATE TABLE `yoshop_goods_collector` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `total_count` int unsigned NOT NULL DEFAULT '0' COMMENT '采集总数量',
  `success_count` int unsigned NOT NULL DEFAULT '0' COMMENT '采集成功数量',
  `fail_count` int unsigned NOT NULL DEFAULT '0' COMMENT '采集失败数量',
  `start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '采集状态(10进行中 20已完成 30失败)',
  `fail_log` text NOT NULL COMMENT '采集失败日志',
  `is_delete` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品采集记录表';

DROP TABLE IF EXISTS `yoshop_store_module`;
CREATE TABLE `yoshop_store_module` (
  `store_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '商城ID',
  `config` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模块内容(json格式)',
  `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家功能模块记录表';

ALTER TABLE `yoshop_payment_trade`
MODIFY COLUMN `trade_state`  tinyint UNSIGNED NOT NULL DEFAULT 10 COMMENT '交易状态(10未支付 20支付成功 30转入退款 40已关闭)' AFTER `prepay_id`;

ALTER TABLE `yoshop_store_menu_api` COMMENT='商家后台菜单与API权限关系表';

ALTER TABLE `yoshop_store_menu_api` MODIFY COLUMN `api_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '后台api ID' AFTER `menu_id`;

ALTER TABLE `yoshop_store_menu`
CHANGE COLUMN `module` `type`  tinyint UNSIGNED NOT NULL DEFAULT 10 COMMENT '菜单类型(10菜单 20操作)' AFTER `menu_id`;

ALTER TABLE `yoshop_store_menu`
ADD COLUMN `module_key`  varchar(100) NOT NULL DEFAULT '' COMMENT '功能模块key' AFTER `path`;

ALTER TABLE `yoshop_store_menu`
MODIFY COLUMN `type`  tinyint UNSIGNED NOT NULL DEFAULT 10 COMMENT '菜单类型(10页面 20操作)' AFTER `menu_id`,
ADD COLUMN `is_page`  tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否为页面(1是 0否)' AFTER `path`;
