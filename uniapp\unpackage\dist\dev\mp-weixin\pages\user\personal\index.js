"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../store/index.js");
const api_user = require("../../../api/user.js");
const api_upload = require("../../../api/upload.js");
const AvatarImage = () => "../../../components/avatar-image/index.js";
const rules = {
  nickName: [{
    required: true,
    message: "请输入用户昵称",
    trigger: ["blur", "change"]
  }]
};
const _sfc_main = {
  components: {
    AvatarImage
  },
  data() {
    return {
      // 按钮禁用
      disabled: false,
      // 头像路径 (用于显示)
      avatarUrl: "",
      // 临时图片 (用于上传)
      tempFile: null,
      // 表单数据
      form: {
        avatarId: "",
        nickName: ""
      },
      // 验证规则
      rules
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getUserInfo();
  },
  // 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    // 获取当前用户信息
    getUserInfo() {
      const app = this;
      api_user.info().then((result) => {
        const userInfo = result.data.userInfo;
        app.avatarUrl = userInfo.avatar_url;
        app.form.avatarId = userInfo.avatar_id;
        app.form.nickName = userInfo.nick_name;
      });
    },
    // 点击头像按钮事件
    onClickAvatar() {
      return;
    },
    // 选择头像事件 - 仅限微信小程序
    onChooseAvatar({ detail }) {
      const app = this;
      app.avatarUrl = detail.avatarUrl;
      app.tempFile = { path: app.avatarUrl };
    },
    // 选择图片
    chooseImage() {
      const app = this;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
        // 可以指定来源是相册还是相机，默认二者都有
        success({ tempFiles }) {
          app.tempFile = tempFiles[0];
          app.avatarUrl = app.tempFile.path;
        }
      });
    },
    // 上传图片
    uploadFile() {
      const app = this;
      return api_upload.image([app.tempFile]).then((fileIds) => {
        app.form.avatarId = fileIds[0];
        app.tempFile = null;
        return true;
      }).catch(() => {
        return false;
      });
    },
    // 确认修改
    async handleSubmit() {
      const app = this;
      if (app.disabled === true)
        return;
      app.$refs.uForm.validate(async (valid) => {
        if (!valid)
          return;
        app.disabled = true;
        if (app.tempFile && !await app.uploadFile()) {
          app.$toast("很抱歉，头像上传失败");
          app.disabled = false;
          return;
        }
        api_user.personal({ form: app.form }).then((result) => {
          app.$toast(result.message);
          setTimeout(() => {
            app.disabled = false;
            common_vendor.index.navigateBack();
          }, 1500);
        }).catch((err) => app.disabled = false);
      });
    },
    // 绑定昵称输入框 (用于微信小程序端快速填写昵称能力)
    onInputNickName(val) {
      if (val) {
        this.form.nickName = val;
      }
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _easycom_u_form_item2 = common_vendor.resolveComponent("u-form-item");
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_form2 = common_vendor.resolveComponent("u-form");
  (_component_avatar_image + _easycom_u_form_item2 + _easycom_u_input2 + _easycom_u_form2)();
}
const _easycom_u_form_item = () => "../../../uni_modules/vk-uview-ui/components/u-form-item/u-form-item.js";
const _easycom_u_input = () => "../../../uni_modules/vk-uview-ui/components/u-input/u-input.js";
const _easycom_u_form = () => "../../../uni_modules/vk-uview-ui/components/u-form/u-form.js";
if (!Math) {
  (_easycom_u_form_item + _easycom_u_input + _easycom_u_form)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      url: $data.avatarUrl,
      width: 100
    }),
    b: common_vendor.o(($event) => $options.onClickAvatar()),
    c: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    d: common_vendor.p({
      label: "头像"
    }),
    e: common_vendor.o($options.onInputNickName),
    f: common_vendor.o($options.onInputNickName),
    g: common_vendor.o(($event) => $data.form.nickName = $event),
    h: common_vendor.p({
      type: "nickname",
      maxlength: "30",
      placeholder: "请输入昵称",
      modelValue: $data.form.nickName
    }),
    i: common_vendor.p({
      label: "昵称",
      prop: "nickName"
    }),
    j: common_vendor.sr("uForm", "4404c214-0"),
    k: common_vendor.p({
      model: $data.form,
      ["label-width"]: "140rpx"
    }),
    l: $data.disabled ? 1 : "",
    m: common_vendor.o(($event) => $options.handleSubmit()),
    n: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4404c214"]]);
wx.createPage(MiniProgramPage);
