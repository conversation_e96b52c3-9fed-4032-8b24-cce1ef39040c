"use strict";
const api_market = require("../../../api/market.js");
const api_myCoupon = require("../../../api/myCoupon.js");
require("../../../common/enum/coupon/ApplyRange.js");
require("../../../common/enum/coupon/ExpireType.js");
const common_enum_coupon_CouponType = require("../../../common/enum/coupon/CouponType.js");
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    // 商品ID
    goodsId: {
      type: Number,
      default: null
    },
    // 商品来源
    goodsSource: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      // 枚举类
      CouponTypeEnum: common_enum_coupon_CouponType.CouponTypeEnum,
      // 正在加载
      isLoading: true,
      // 显示活动详情弹层
      showPopup: false,
      // 显示活动描述弹窗
      descModal: {
        show: false,
        title: "",
        content: ""
      },
      // 优惠列表
      marketList: [],
      // 优惠券列表
      couponList: []
    };
  },
  created() {
    this.getMarketDetail();
  },
  methods: {
    // 获取优惠详情
    getMarketDetail() {
      const app = this;
      app.isLoading = true;
      api_market.detail({ goodsId: app.goodsId || 0, source: app.goodsSource }).then((result) => {
        app.marketList = result.data.marketList;
        app.couponList = result.data.couponList;
      }).finally(() => app.isLoading = false);
    },
    // 显示弹窗
    handlePopup() {
      this.showPopup = !this.showPopup;
    },
    // 立即领取
    handleReceive(couponId) {
      const app = this;
      api_myCoupon.receive(couponId).then((result) => {
        app.$success(result.message);
        app.getMarketDetail();
      });
    },
    // 展开优惠券描述
    handleDescribe(index) {
      this.couponList[index].expand = !this.couponList[index].expand;
    },
    // 显示活动描述内容
    handleContent(index) {
      const item = this.marketList[index];
      if (!item.describe) {
        return;
      }
      this.descModal.show = true;
      this.descModal.title = item.tagName;
      this.descModal.content = item.describe;
    }
  }
};
if (!Array) {
  const _easycom_u_tag2 = common_vendor.resolveComponent("u-tag");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_easycom_u_tag2 + _easycom_u_popup2 + _easycom_u_modal2)();
}
const _easycom_u_tag = () => "../../../uni_modules/vk-uview-ui/components/u-tag/u-tag.js";
const _easycom_u_popup = () => "../../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
const _easycom_u_modal = () => "../../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_u_tag + _easycom_u_popup + _easycom_u_modal)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.couponList.length || $data.marketList.length
  }, $data.couponList.length || $data.marketList.length ? common_vendor.e({
    b: $data.couponList.length
  }, $data.couponList.length ? {
    c: common_vendor.f($data.couponList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index
      };
    }),
    d: common_vendor.o(($event) => $options.handlePopup())
  } : {}, {
    e: $data.marketList.length
  }, $data.marketList.length ? {
    f: common_vendor.f($data.marketList, (item, index, i0) => {
      return common_vendor.e({
        a: item.isFirst
      }, item.isFirst ? {
        b: "f67ec2c2-0-" + i0,
        c: common_vendor.p({
          color: _ctx.appTheme.mainBg,
          ["border-color"]: _ctx.appTheme.mainBg,
          text: item.tagName,
          type: "error",
          size: "mini",
          mode: "plain"
        }),
        d: common_vendor.t(item.title)
      } : {}, {
        e: index
      });
    }),
    g: common_vendor.o(($event) => $options.handlePopup())
  } : {}) : {}, {
    h: $data.marketList.length
  }, $data.marketList.length ? {} : {}, {
    i: $data.marketList.length
  }, $data.marketList.length ? {
    j: common_vendor.f($data.marketList, (item, index, i0) => {
      return common_vendor.e({
        a: "f67ec2c2-2-" + i0 + ",f67ec2c2-1",
        b: common_vendor.p({
          color: _ctx.appTheme.mainBg,
          ["border-color"]: _ctx.appTheme.mainBg,
          text: item.tagName,
          type: "error",
          size: "mini",
          mode: "plain"
        }),
        c: common_vendor.t(item.title),
        d: item.describe
      }, item.describe ? {} : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.handleContent(index), index)
      });
    })
  } : {}, {
    k: $data.couponList.length
  }, $data.couponList.length ? {} : {}, {
    l: $data.couponList.length
  }, $data.couponList.length ? {
    m: common_vendor.f($data.couponList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($data.CouponTypeEnum[item.coupon_type].name),
        b: item.coupon_type == $data.CouponTypeEnum.FULL_DISCOUNT.value
      }, item.coupon_type == $data.CouponTypeEnum.FULL_DISCOUNT.value ? {
        c: common_vendor.t(item.reduce_price)
      } : {}, {
        d: item.coupon_type == $data.CouponTypeEnum.DISCOUNT.value
      }, item.coupon_type == $data.CouponTypeEnum.DISCOUNT.value ? {
        e: common_vendor.t(item.discount)
      } : {}, {
        f: common_vendor.t(item.min_price),
        g: common_vendor.t(item.name),
        h: item.expire_type == $data.CouponTypeEnum.FULL_DISCOUNT.value
      }, item.expire_type == $data.CouponTypeEnum.FULL_DISCOUNT.value ? {
        i: common_vendor.t(item.expire_day)
      } : {}, {
        j: item.expire_type == $data.CouponTypeEnum.DISCOUNT.value
      }, item.expire_type == $data.CouponTypeEnum.DISCOUNT.value ? common_vendor.e({
        k: item.start_time === item.end_time
      }, item.start_time === item.end_time ? {
        l: common_vendor.t(item.start_time)
      } : {
        m: common_vendor.t(item.start_time),
        n: common_vendor.t(item.end_time)
      }) : {}, {
        o: item.describe
      }, item.describe ? {
        p: common_vendor.n(item.expand ? "expand" : ""),
        q: common_vendor.o(($event) => $options.handleDescribe(index), index)
      } : {}, {
        r: _ctx.$checkModule("market-coupon") && item.state.value
      }, _ctx.$checkModule("market-coupon") && item.state.value ? {
        s: common_vendor.o(($event) => $options.handleReceive(item.coupon_id), index)
      } : {}, {
        t: !item.state.value
      }, !item.state.value ? {
        v: common_vendor.t(item.state.text)
      } : {}, {
        w: common_vendor.n(!item.state.value ? "disable" : ""),
        x: common_vendor.t(item.describe),
        y: common_vendor.n(item.expand ? "expand" : ""),
        z: index
      });
    })
  } : {}, {
    n: common_vendor.o(($event) => $data.showPopup = $event),
    o: common_vendor.p({
      mode: "bottom",
      closeable: true,
      ["border-radius"]: 26,
      modelValue: $data.showPopup
    }),
    p: common_vendor.t($data.descModal.content),
    q: common_vendor.o(($event) => $data.descModal.show = $event),
    r: common_vendor.p({
      title: $data.descModal.title,
      modelValue: $data.descModal.show
    }),
    s: common_vendor.s(_ctx.appThemeStyle)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f67ec2c2"]]);
wx.createComponent(Component);
