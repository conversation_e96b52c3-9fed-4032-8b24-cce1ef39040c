<?php return array(
    'root' => array(
        'name' => 'topthink/think',
        'pretty_version' => 'dev-dev未加密',
        'version' => 'dev-dev未加密',
        'reference' => '7f93d97be14576b7507983c17a67048737a63359',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'adbario/php-dot-notation' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => 'eee4fc81296531e6aafba4c2bbccfc5adab1676e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../adbario/php-dot-notation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aferrandini/phpqrcode' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '3c1c0454d43710ab5bbe19a51ad4cb41c22e3d46',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aferrandini/phpqrcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'alibabacloud/tea' => array(
            'pretty_version' => '3.1.23',
            'version' => '3.1.23.0',
            'reference' => '61fce993274edf6e7131af07256ed7723d97a85f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alibabacloud/tea',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'alibabacloud/tea-fileform' => array(
            'pretty_version' => '0.3.4',
            'version' => '*******',
            'reference' => '4bf0c75a045c8115aa8cb1a394bd08d8bb833181',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alibabacloud/tea-fileform',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'alipaysdk/easysdk' => array(
            'pretty_version' => '2.2.1',
            'version' => '*******',
            'reference' => '066388d02c6f55fe0919d75b386456d80801fec2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alipaysdk/easysdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aliyuncs/oss-sdk-php' => array(
            'pretty_version' => 'v2.4.3',
            'version' => '*******',
            'reference' => '4ccead614915ee6685bf30016afb01aabd347e46',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aliyuncs/oss-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'danielstjules/stringy' => array(
            'pretty_version' => '3.1.0',
            'version' => '*******',
            'reference' => 'df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../danielstjules/stringy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'easywechat-composer/easywechat-composer' => array(
            'pretty_version' => '1.4.1',
            'version' => '*******',
            'reference' => '3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../easywechat-composer/easywechat-composer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.14.0',
            'version' => '4.14.0.0',
            'reference' => '12ab42bd6e742c70c0a52f7b82477fcd44e64b75',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/command' => array(
            'pretty_version' => '1.2.2',
            'version' => '1.2.2.0',
            'reference' => '7883359e0ecab8a8f7c43aad2fc36360a35d21e8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/command',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.4.2',
            'version' => '7.4.2.0',
            'reference' => 'ac1ec1cd9b5624694c3a40be801d94137afb12b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle-services' => array(
            'pretty_version' => '1.3.2',
            'version' => '1.3.2.0',
            'reference' => '4989d902dd4e0411b320e851c46f3c94d652d891',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle-services',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.1',
            'version' => '1.5.1.0',
            'reference' => 'fe752aedc9fd8fcca3fe7ad05d419d32998a06da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.2.1',
            'version' => '*******',
            'reference' => 'c94a94f120803a18554c1805ef2e539f8285f9a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'b945d74a55a25a949158444f09ec0d3c120d69e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kosinix/grafika' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '211f61fc334b8b36616b23e8af7c5727971d96ee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kosinix/grafika',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '2.5.0',
            'version' => '*******',
            'reference' => '8aaffb653c5777781b0f7f69a5d937baf7ab6cdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => 'ff6248ea87a9f116e78edd6002e39e5128a0d4dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lvht/geohash' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '*******',
            'reference' => 'bbba3e1b487f0ec2e5e666c1bc9d1d4277990a29',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lvht/geohash',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '2.1.0',
            'version' => '*******',
            'reference' => 'c4c5803cc1f93df3d2448478ef79394a5981cc58',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => 'ab8bc271e404909db09ff2d5ffa1e538085c0f22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.0',
            'version' => '*******',
            'reference' => 'c66aefcafb4f6c269510e9ac46b82619a904c576',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.5.0',
            'version' => '*******',
            'reference' => '4192345e260f1d51b365536199744b987e160edc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => '9b87907a81b87bc76d19a7fb2d61e61486ee9edb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.6.6',
            'version' => '1.6.6.0',
            'reference' => '32c4202886c51fbe5cc3a7c34ec5c9a4a790345e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.66.0',
            'version' => '2.66.0.0',
            'reference' => '496712849902241f04902033b0441b269effe001',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'overtrue/easy-sms' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => 'fda1b6fcde861451ccf54e1071b4e1877455d89a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/easy-sms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'overtrue/socialite' => array(
            'pretty_version' => '2.0.24',
            'version' => '2.0.24.0',
            'reference' => 'ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/socialite',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'overtrue/wechat' => array(
            'pretty_version' => '4.5.0',
            'version' => '4.5.0.0',
            'reference' => '04a940f97d6812a67bb8d5f2dbaebf9ad78ae776',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/wechat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.22.0',
            'version' => '1.22.0.0',
            'reference' => '3a9e29b4f386a08a151a33578e80ef1747037a48',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pimple/pimple' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'a94b3a4db7fb774b3d78dad2315ddc07629e1bed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pimple/pimple',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '2dfb5f6c5eff0e91e20e913f8c5452ed95b86621',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'qcloud/cos-sdk-v5' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'reference' => '3b2f32efd6c7bba7b982b118c5af18db74658fc5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qcloud/cos-sdk-v5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'qiniu/php-sdk' => array(
            'pretty_version' => 'v7.4.3',
            'version' => '7.4.3.0',
            'reference' => 'ee5344f319dd7babd86c9a38e6bd05aa58dc90ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qiniu/php-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'songshenzong/support' => array(
            'pretty_version' => '2.0.6',
            'version' => '2.0.6.0',
            'reference' => 'b334d8abc99e8a85538a556e10c670c18b71c230',
            'type' => 'library',
            'install_path' => __DIR__ . '/../songshenzong/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v5.4.7',
            'version' => '5.4.7.0',
            'reference' => 'ba06841ed293fcaf79a592f59fdaba471f7c756c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'reference' => '64be4a7acb83b6f2bf6de9a02cee6dad41277ebc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '*******',
            'reference' => '26954b3d62a6c5fd0ea8a2a00c0353a14978d05c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v5.4.3',
            'version' => '5.4.3.0',
            'reference' => 'dec8a9f58d20df252b9cd89f1c6c1530f747685d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '*******',
            'reference' => '7bc61cc2db649b4637d331240c5346dcc7708051',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0',
            ),
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v5.4.6',
            'version' => '5.4.6.0',
            'reference' => '34e89bc147633c0f9dd6caaaf56da3b806a21465',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'reference' => '0abb51d2f102e00a4eefcf46ba7fec406d245825',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'reference' => '9a142215a36a3888e30d0a9eeea9766764e96976',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'reference' => 'cc5db0e22b3cb4111010e48785a97f670b350ca5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'reference' => '4407588e0d3f1f52efb65fbe92babe41f37fe50c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '4.4.44.0',
            'reference' => '5cee9cdc4f7805e2699d9fd66991a0e6df8252a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v2.1.2',
            'version' => '2.1.2.0',
            'reference' => '22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'reference' => '24d9dc654b83e91aa59f9d167b131bc3b5bea24c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.2.5',
            'version' => '6.2.5.0',
            'reference' => '60556925a703cfbc1581cde3b3f35b0bb0ea904c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '3.2.0.0',
            'reference' => '68cce71402305a015f8c1589bfada1280dc64fe7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v4.4.39',
            'version' => '4.4.39.0',
            'reference' => '35237c5e5dcb6593a46a860ba5b29c1d4683d80e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v6.0.7',
            'version' => '6.0.7.0',
            'reference' => '5f1fddb1b3a8394dbfb234044e3ad620a26e1735',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v6.1.2',
            'version' => '6.1.2.0',
            'reference' => '67235be5b919aaaf1de5aed9839f65d8e766aca3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => 'dev-dev未加密',
            'version' => 'dev-dev未加密',
            'reference' => '7f93d97be14576b7507983c17a67048737a63359',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-filesystem' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '50af34c4cfc9a5cbe8a5e3ac9f4e2aa0fd90693f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v3.1.6',
            'version' => '3.1.6.0',
            'reference' => '769acbe50a4274327162f9c68ec2e89a38eb2aff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-multi-app' => array(
            'pretty_version' => 'v1.0.14',
            'version' => '1.0.14.0',
            'reference' => 'ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-multi-app',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-orm' => array(
            'pretty_version' => 'v2.0.53',
            'version' => '2.0.53.0',
            'reference' => '06783eda65547a70ea686360a897759e1f873fff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-queue' => array(
            'pretty_version' => 'v3.0.7',
            'version' => '3.0.7.0',
            'reference' => 'cded7616e313f9daa55c0ad0de5791f0d1fb3066',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-queue',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-worker' => array(
            'pretty_version' => 'v3.0.6',
            'version' => '3.0.6.0',
            'reference' => '21dc442aaa50594466ed3ed767af0a68b8b75364',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-worker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wechatpay/wechatpay' => array(
            'pretty_version' => '1.4.6',
            'version' => '1.4.6.0',
            'reference' => 'edbdb6bb19e0818b0576043b265ff1b1e188d668',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wechatpay/wechatpay',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/gateway-worker' => array(
            'pretty_version' => 'v3.0.22',
            'version' => '3.0.22.0',
            'reference' => 'a615036c482d11f68b693998575e804752ef9068',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/gateway-worker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/workerman' => array(
            'pretty_version' => 'v3.5.31',
            'version' => '3.5.31.0',
            'reference' => 'b73ddc45b3c7299f330923a2bde23ca6e974fd96',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/workerman',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'xin/container' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '97bb67f87dd851545938a1f2fe0ffbd379e3ff81',
            'type' => 'library',
            'install_path' => __DIR__ . '/../xin/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'xin/helper' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => '02a58132dae2aea2d1c0b8e66f55125969224747',
            'type' => 'library',
            'install_path' => __DIR__ . '/../xin/helper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiovo/tp6-cache' => array(
            'pretty_version' => 'v1.0.1',
            'version' => '1.0.1.0',
            'reference' => '31e0b5aaa1315ef85d7e68ed189bdb68406becfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yiovo/tp6-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiovo/tp6-captcha' => array(
            'pretty_version' => 'v1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'ad04954c2c3de274f5bd7549788094ee96e4160e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yiovo/tp6-captcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
