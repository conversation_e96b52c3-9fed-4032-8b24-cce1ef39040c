"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  myList: "groupon.task/myList",
  listByGoods: "groupon.task/listByGoods",
  detail: "groupon.task/detail"
};
const myList = (param) => {
  return utils_request_index.$http.get(api.myList, param);
};
const detail = (taskId, param) => {
  return utils_request_index.$http.get(api.detail, { taskId, ...param });
};
const listByGoods = (grouponGoodsId, param) => {
  return utils_request_index.$http.get(api.listByGoods, { grouponGoodsId, ...param });
};
exports.detail = detail;
exports.listByGoods = listByGoods;
exports.myList = myList;
