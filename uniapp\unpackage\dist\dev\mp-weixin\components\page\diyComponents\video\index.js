"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "Videos",
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: `${$props.itemStyle.height * 2}rpx`,
    b: $props.params.videoUrl,
    c: $props.params.poster,
    d: $props.params.autoplay == 1,
    e: `${$props.itemStyle.paddingTop * 2}rpx 0`
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2dad04a9"]]);
wx.createComponent(Component);
