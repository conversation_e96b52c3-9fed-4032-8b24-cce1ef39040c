"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "sharp.goods/list",
  detail: "sharp.goods/detail",
  poster: "sharp.goods/poster"
};
const list = (activeTimeId, param, option) => {
  return utils_request_index.$http.get(api.list, { activeTimeId, ...param }, option);
};
const detail = (activeTimeId, sharpGoodsId, param) => {
  return utils_request_index.$http.get(api.detail, { activeTimeId, sharpGoodsId, ...param });
};
const poster = (param) => {
  return utils_request_index.$http.get(api.poster, param);
};
exports.detail = detail;
exports.list = list;
exports.poster = poster;
