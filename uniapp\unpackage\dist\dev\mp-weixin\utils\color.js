"use strict";
const hex2rgba = (color, opacity) => {
  var theColor = color.toLowerCase();
  var r = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  if (theColor && r.test(theColor)) {
    if (theColor.length === 4) {
      var sColorNew = "#";
      for (var i = 1; i < 4; i += 1) {
        sColorNew += theColor.slice(i, i + 1).concat(theColor.slice(i, i + 1));
      }
      theColor = sColorNew;
    }
    var sColorChange = [];
    for (var i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt("0x" + theColor.slice(i, i + 2)));
    }
    return "rgba(" + sColorChange.join(",") + "," + opacity + ")";
  }
  return theColor;
};
exports.hex2rgba = hex2rgba;
