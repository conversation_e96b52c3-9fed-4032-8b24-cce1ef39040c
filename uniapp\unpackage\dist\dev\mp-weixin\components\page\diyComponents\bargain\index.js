"use strict";
const utils_util = require("../../../../utils/util.js");
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const AvatarImage = () => "../../../avatar-image/index.js";
const _sfc_main = {
  components: {
    AvatarImage
  },
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  data() {
    return { inArray: utils_util.inArray };
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 跳转到砍价商品详情
    handleNavDetail(item) {
      this.$navTo("pages/bargain/goods/index", { activeId: item.active_id, goodsId: item.goods_id });
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  _component_avatar_image();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (goods, idx, i0) => {
      return common_vendor.e({
        a: goods.goods_image
      }, $data.inArray("goodsName", $props.itemStyle.show) ? {
        b: common_vendor.t(goods.goods_name)
      } : {}, {
        c: $data.inArray("peoples", $props.itemStyle.show) && goods.helpsCount
      }, $data.inArray("peoples", $props.itemStyle.show) && goods.helpsCount ? {
        d: common_vendor.f(goods.helpList, (help, hidx, i1) => {
          return {
            a: "647596e4-0-" + i0 + "-" + i1,
            b: common_vendor.p({
              url: help.user.avatar_url,
              width: 32
            }),
            c: hidx
          };
        }),
        e: common_vendor.t(goods.helpsCount)
      } : {}, $data.inArray("originalPrice", $props.itemStyle.show) ? {
        f: common_vendor.t(goods.original_price)
      } : {}, $data.inArray("floorPrice", $props.itemStyle.show) ? {
        g: common_vendor.t(goods.floor_price)
      } : {}, {
        h: idx,
        i: common_vendor.o(($event) => $options.handleNavDetail(goods), idx)
      });
    }),
    b: $data.inArray("goodsName", $props.itemStyle.show),
    c: $data.inArray("originalPrice", $props.itemStyle.show),
    d: $data.inArray("floorPrice", $props.itemStyle.show),
    e: $props.itemStyle.background
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-647596e4"]]);
wx.createComponent(Component);
