"use strict";
const utils_request_index = require("../utils/request/index.js");
const image = (files, checkLogin = true) => {
  const maxSize = 1024 * 1024 * 2;
  return new Promise((resolve, reject) => {
    utils_request_index.$http.urlFileUpload({ name: "file", files, maxSize, data: { test: 123, checkLogin: Number(checkLogin) } }).then((result) => resolve(result.map((item) => item.data.fileInfo.file_id), result)).catch(reject);
  });
};
exports.image = image;
