"use strict";
const common_vendor = require("../../common/vendor.js");
const api_dealer_apply = require("../../api/dealer/apply.js");
const common_model_dealer_Setting = require("../../common/model/dealer/Setting.js");
const _sfc_main = {
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 当前是否为分销商
      isDealer: void 0,
      // 当前是否在申请中
      isApplying: void 0,
      // 推荐人昵称
      refereeName: void 0,
      // 文字设置
      words: void 0,
      // 背景图
      background: void 0,
      // 入驻协议
      license: void 0,
      // 入驻协议阅读状态
      isRead: true,
      // 显示入驻协议弹窗
      showLicense: false,
      // 按钮禁用
      disabled: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getSetting();
    this.getApplyStatus();
  },
  methods: {
    // 获取分销设置
    getSetting() {
      const app = this;
      common_model_dealer_Setting.SettingModel.data().then((setting) => {
        app.words = setting.words.apply.words;
        app.background = setting.background.apply;
        app.license = setting.license.license;
        app.setPageTitle(setting.words.apply.title);
      });
    },
    // 分销商申请状态
    getApplyStatus() {
      const app = this;
      app.isLoading = true;
      api_dealer_apply.status().then((result) => {
        const data = result.data;
        app.isDealer = data.isDealer;
        app.isApplying = data.isApplying;
        app.refereeName = data.refereeName;
      }).finally(() => app.isLoading = false);
    },
    // 设置当前页面标题
    setPageTitle(title) {
      common_vendor.index.setNavigationBarTitle({ title: title.value });
    },
    // 切换支付选项
    handleChecked(value) {
      this.payment = value;
    },
    // 显示入驻协议弹窗
    handleShowLicense() {
      this.showLicense = true;
    },
    // 表单提交
    handleSubmit({ detail }) {
      const app = this;
      if (!app.onValidation(detail.value)) {
        return false;
      }
      app.disabled = true;
      api_dealer_apply.submit({ form: detail.value }).then((result) => {
        app.$toast(result.message);
        setTimeout(() => common_vendor.index.navigateBack(), 1200);
      }).finally(() => app.disabled = false);
    },
    // 表单验证
    onValidation(data) {
      const app = this;
      if (!data.name) {
        app.$error("请填写姓名");
        return false;
      }
      if (!/^\+?\d[\d -]{8,12}\d/.test(data.mobile)) {
        app.$error("手机号格式不正确");
        return false;
      }
      if (!app.isRead) {
        app.$error("请先阅读分销商申请协议");
        return false;
      }
      return true;
    }
  }
};
if (!Array) {
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  _easycom_u_modal2();
}
const _easycom_u_modal = () => "../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  _easycom_u_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.background,
    c: $data.isApplying
  }, $data.isApplying ? {
    d: common_vendor.t($data.words.wait_audit.value),
    e: common_vendor.t($data.words.goto_mall.value),
    f: common_vendor.o(($event) => _ctx.$navTo("pages/index/index"))
  } : {
    g: common_vendor.t($data.words.title.value),
    h: common_vendor.t($data.refereeName),
    i: common_vendor.t($data.words.submit.value),
    j: $data.disabled,
    k: common_vendor.o((...args) => $options.handleSubmit && $options.handleSubmit(...args))
  }, {
    l: common_vendor.t($data.license),
    m: common_vendor.o(($event) => $data.showLicense = $event),
    n: common_vendor.p({
      title: "申请协议",
      modelValue: $data.showLicense
    })
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7bd142cd"]]);
wx.createPage(MiniProgramPage);
