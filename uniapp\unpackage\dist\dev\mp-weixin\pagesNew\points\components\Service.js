"use strict";
const api_goods_service = require("../../../api/goods/service.js");
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    // 商品ID
    goodsId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 显示详情内容弹窗
      showPopup: false,
      // 服务列表数据
      list: []
    };
  },
  created() {
    this.getServiceList();
  },
  methods: {
    // 获取商品服务列表
    getServiceList() {
      const app = this;
      app.isLoading = true;
      api_goods_service.list(app.goodsId).then((result) => app.list = result.data.list).finally(() => app.isLoading = false);
    },
    // 显示弹窗
    handlePopup() {
      this.showPopup = !this.showPopup;
    }
  }
};
if (!Array) {
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  _easycom_u_popup2();
}
const _easycom_u_popup = () => "../../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
if (!Math) {
  _easycom_u_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.list.length
  }, $data.list.length ? {
    b: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index
      };
    }),
    c: common_vendor.o((...args) => $options.handlePopup && $options.handlePopup(...args)),
    d: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.summary),
        c: index
      };
    }),
    e: common_vendor.o(($event) => $data.showPopup = $event),
    f: common_vendor.p({
      mode: "bottom",
      closeable: true,
      ["border-radius"]: 26,
      modelValue: $data.showPopup
    }),
    g: common_vendor.s(_ctx.appThemeStyle)
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-28b9d8e2"]]);
wx.createComponent(Component);
