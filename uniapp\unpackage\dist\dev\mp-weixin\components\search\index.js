"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    tips: {
      type: String,
      default: "搜索商品"
    }
  },
  data() {
    return {};
  },
  methods: {
    onClick() {
      this.$emit("event");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($props.tips),
    b: common_vendor.o((...args) => $options.onClick && $options.onClick(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bcaca87a"]]);
wx.createComponent(Component);
