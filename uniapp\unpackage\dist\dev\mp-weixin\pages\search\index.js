"use strict";
const common_vendor = require("../../common/vendor.js");
const HISTORY_SEARCH = "historySearch";
const _sfc_main = {
  data() {
    return {
      historySearch: [],
      searchValue: ""
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.historySearch = this.getHistorySearch();
  },
  methods: {
    /**
     * 获取历史搜索
     */
    getHistorySearch() {
      return common_vendor.index.getStorageSync(HISTORY_SEARCH) || [];
    },
    /**
     * 搜索提交
     */
    onSearch() {
      const { searchValue } = this;
      if (searchValue) {
        this.setHistory(searchValue);
        this.$navTo("pages/goods/list", { search: searchValue });
      }
    },
    /**
     * 记录历史搜索
     */
    setHistory(searchValue) {
      const data = this.getHistorySearch();
      const index = data.indexOf(searchValue);
      index > -1 && data.splice(index, 1);
      data.unshift(searchValue);
      this.historySearch = data;
      this.onUpdateStorage();
    },
    /**
     * 清空最近搜索记录
     */
    clearSearch() {
      this.historySearch = [];
      this.onUpdateStorage();
    },
    /**
     * 更新历史搜索缓存
     * @param {Object} data
     */
    onUpdateStorage(data) {
      common_vendor.index.setStorageSync(HISTORY_SEARCH, this.historySearch);
    },
    /**
     * 跳转到最近搜索
     */
    handleQuick(search) {
      this.$navTo("pages/goods/list", { search });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.searchValue,
    b: common_vendor.o(($event) => $data.searchValue = $event.detail.value),
    c: common_vendor.o((...args) => $options.onSearch && $options.onSearch(...args)),
    d: $data.historySearch.length
  }, $data.historySearch.length ? {
    e: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args)),
    f: common_vendor.f($data.historySearch, (val, index, i0) => {
      return {
        a: common_vendor.t(val),
        b: common_vendor.o(($event) => $options.handleQuick(val), index),
        c: index
      };
    })
  } : {}, {
    g: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2dab939d"]]);
wx.createPage(MiniProgramPage);
