"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "xj.video/list",
  listDetail: "xj.video/listDetail",
  detail: "xj.video/detail",
  addView: "xj.video/addView"
};
function list(param, option) {
  return utils_request_index.$http.get(api.list, param, option);
}
function listDetail(categoryId, videoId, page) {
  return utils_request_index.$http.get(api.listDetail, { categoryId, videoId, page });
}
function detail(articleId) {
  return utils_request_index.$http.get(api.detail, { articleId });
}
function addView(articleId) {
  return utils_request_index.$http.get(api.addView, { articleId });
}
exports.addView = addView;
exports.detail = detail;
exports.list = list;
exports.listDetail = listDetail;
