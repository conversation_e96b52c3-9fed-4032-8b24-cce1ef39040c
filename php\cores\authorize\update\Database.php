<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores\authorize\update;

use cores\library\Download;
use cores\library\backup\DataBase as DataBaseBackup;
use cores\exception\BaseException;

/**
 * 一键更新服务(数据库)
 * Class Database
 */
class Database
{
    // 当前要升级的版本号
    private string $version;

    // 当前的模块信息
    private array $moduleInfo = [];

    // 数据库文件名称 (下载后)
    private string $fileName = 'upgrade.sql';

    // 下载类的实例
    private static $downloadInstantiate;

    // 数据库备份类的实例
    private static $dataBaseBackupInstantiate;

    /**
     * 构造方法
     * @param string $version
     * @param array $moduleInfo
     */
    public function __construct(string $version, array $moduleInfo)
    {
        $this->version = $version;
        $this->moduleInfo = $moduleInfo;
    }

    /**
     * 执行一键更新服务端
     * @throws BaseException
     */
    public function handle()
    {
        if (!empty($this->moduleInfo['download_url'])) {
            // 1. 备份数据库表
            $this->backup();
            // 2. 下载升级包(数据库)
            $this->download();
            // 3. 导入升级包(数据库)
            $this->import();
        }
    }

    /**
     * 备份指定的数据库
     * @throws BaseException
     */
    private function backup()
    {
        if (empty($this->moduleInfo['update_content'])) {
            return;
        }
        // 备份的表名
        $tableNames = $this->moduleInfo['update_content'];
        foreach ($tableNames as $tableName) {
            $this->getDataBaseBackupInstantiate()->backup($tableName, 0);
        }
    }

    /**
     * 下载升级包(数据库文件)
     * @return void
     * @throws BaseException
     */
    private function download()
    {
        $this->getDownloadInstantiate()->download();
        if (!file_exists($this->getDownloadInstantiate()->getFilePath())) {
            throwError('数据库文件不存在');
        }
    }

    /**
     * 导入升级包(数据库文件)
     * @throws BaseException
     */
    private function import()
    {
        $sqlFile = $this->getDownloadInstantiate()->getFilePath();
        if (!$this->getDataBaseBackupInstantiate()->import($sqlFile)) {
            throwError('数据库文件导入失败');
        }
    }

    /**
     * 获取备份文件夹路径
     * @return string
     */
    private function getBackupPath(): string
    {
        return runtime_root_path() . "admin/backup/v{$this->version}/database/";
    }

    /**
     * 获取数据库备份类的实例
     * @return DataBaseBackup
     * @throws BaseException
     */
    private function getDataBaseBackupInstantiate(): DataBaseBackup
    {
        if (!self::$dataBaseBackupInstantiate) {
            self::$dataBaseBackupInstantiate = new DataBaseBackup([
                'path' => $this->getBackupPath(),
                'part' => 3145728,
                'compress' => 0,
                'level' => '1',
            ]);
        }
        return self::$dataBaseBackupInstantiate;
    }

    /**
     * 获取下载类的实例
     * @return Download
     */
    private function getDownloadInstantiate(): Download
    {
        if (!self::$downloadInstantiate) {
            $folderPath = runtime_root_path() . "admin/update/v{$this->version}/";
            self::$downloadInstantiate = (new Download)->setFolderPath($folderPath)
                ->setFileName($this->fileName)
                ->setUrl($this->moduleInfo['download_url']);
        }
        return self::$downloadInstantiate;
    }
}