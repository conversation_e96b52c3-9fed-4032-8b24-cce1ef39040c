"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  userInfo: "user/info",
  assets: "user/assets",
  bindMobile: "user/bindMobile",
  add: "user/add",
  personal: "user/personal",
  addressEdit: "user/addressEdit"
};
const info = (param, option) => {
  const options = {
    isPrompt: true,
    //（默认 true 说明：本接口抛出的错误是否提示）
    load: true,
    //（默认 true 说明：本接口是否提示加载动画）
    ...option
  };
  return utils_request_index.$http.get(api.userInfo, param, options);
};
const assets = (param, option) => {
  return utils_request_index.$http.get(api.assets, param, option);
};
const add = (data, option) => {
  return utils_request_index.$http.post(api.add, data, option);
};
const bindMobile = (data, option) => {
  return utils_request_index.$http.post(api.bindMobile, data, option);
};
const personal = (data, option) => {
  return utils_request_index.$http.post(api.personal, data, option);
};
exports.add = add;
exports.assets = assets;
exports.bindMobile = bindMobile;
exports.info = info;
exports.personal = personal;
