"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_util = require("../../utils/util.js");
const core_app = require("../../core/app.js");
const common_enum_setting_Key = require("../../common/enum/setting/Key.js");
const common_model_Store = require("../../common/model/Store.js");
const common_model_Setting = require("../../common/model/Setting.js");
const api_user = require("../../api/user.js");
const api_article_index = require("../../api/article/index.js");
const api_order = require("../../api/order.js");
const common_assets = require("../../common/assets.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const Recommended = () => "../../components/recommended/index.js";
const CustomerBtn = () => "../../components/customer-btn/index.js";
const PromotePopup = () => "../../components/promote-popup/index.js";
const MpWeixinMobile = () => "../../components/mp-weixin-mobile/index.js";
const orderNavbar = [
  {
    id: "all",
    name: "全部订单",
    icon: "qpdingdan"
  },
  {
    id: "payment",
    name: "待支付",
    icon: "daifukuan",
    count: 0
  },
  {
    id: "delivery",
    name: "待发货",
    icon: "daifahuo",
    count: 0
  },
  {
    id: "received",
    name: "待收货",
    icon: "daishouhuo",
    count: 0
  }
];
const service = [
  {
    id: "address",
    name: "收货地址",
    icon: "shouhuodizhi",
    type: "link",
    url: "pages/address/index"
  },
  {
    id: "refund",
    name: "退换/售后",
    icon: "shouhou",
    type: "link",
    url: "pages/refund/index",
    count: 0
  },
  {
    id: "contact",
    name: "在线客服",
    icon: "kefu",
    type: "contact"
  },
  {
    id: "points",
    name: "我的积分",
    icon: "jifen",
    type: "link",
    url: "pages/points/log",
    moduleKey: "market-points"
  },
  {
    id: "dealer",
    name: "推广中心",
    icon: "fenxiao",
    type: "link",
    url: "pages/dealer/index",
    moduleKey: "apps-dealer"
  },
  {
    id: "dealer",
    name: "提现中心",
    icon: "jifen",
    type: "link",
    url: "pagesNew/tixian/center",
    moduleKey: "apps-dealer"
  },
  {
    id: "orderCenter",
    name: "订单中心",
    icon: "order-c",
    type: "link",
    url: "pages/order/center"
  },
  {
    id: "help",
    name: "我的帮助",
    icon: "bangzhu",
    type: "link",
    url: "pages/help/index",
    moduleKey: "content-help"
  }
];
const _sfc_main = {
  components: {
    AvatarImage,
    Recommended,
    CustomerBtn,
    PromotePopup,
    MpWeixinMobile
  },
  data() {
    return {
      showLogin: false,
      article: {},
      vipShow: false,
      inArray: utils_util.inArray,
      // 枚举类
      SettingKeyEnum: common_enum_setting_Key.SettingKeyEnum,
      // 正在加载
      isLoading: true,
      // 首次加载
      isFirstload: true,
      // 是否已登录
      isLogin: false,
      // 系统设置
      setting: {},
      // 当前用户信息
      userInfo: {},
      // 账户资产
      assets: {
        balance: "--",
        points: "--",
        coupon: "--"
      },
      // 我的服务
      service,
      // 订单操作
      orderNavbar,
      // 当前用户待处理的订单数量
      todoCounts: {
        payment: 0,
        deliver: 0,
        received: 0
      }
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onLoad(options) {
    if (options.showLogin == "true") {
      this.showLogin = true;
      this.isLoading = false;
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    common_vendor.index.$on("event", (showLogin) => {
      this.showLogin = showLogin;
    });
    common_vendor.index.$on("login", (showLogin) => {
      this.onRefreshPage();
    });
    this.vipShow = false;
    this.onRefreshPage();
  },
  methods: {
    toSign() {
      common_vendor.index.navigateTo({
        url: "/pages/sign/index"
      });
    },
    // 刷新页面
    onRefreshPage() {
      core_app.setCartTabBadge();
      this.isLogin = core_app.checkLogin();
      this.getPageData();
    },
    // 获取页面数据
    getPageData(callback) {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getSetting(), app.getUserInfo(), app.getUserAssets(), app.getTodoCounts()]).then((result) => {
        app.isFirstload = false;
        app.initService();
        app.initOrderTabbar();
        callback && callback();
      }).catch((err) => console.log("catch", err)).finally(() => app.isLoading = false);
    },
    // 初始化我的服务数据
    async initService() {
      const app = this;
      const isEnabledDealer = await common_model_Store.StoreModel.isEnabledDealer();
      const isShowCustomerBtn = await common_model_Setting.SettingModel.isShowCustomerBtn();
      const newService = [];
      service.forEach((item) => {
        item.enabled = true;
        if (item.id === "points") {
          item.name = "我的" + app.setting[common_enum_setting_Key.SettingKeyEnum.POINTS.value].points_name;
        }
        if (item.id === "dealer" && !isEnabledDealer) {
          item.enabled = false;
        }
        if (item.id === "contact" && !isShowCustomerBtn) {
          item.enabled = false;
        }
        if (item.count != void 0) {
          item.count = app.todoCounts[item.id];
        }
        newService.push(item);
      });
      app.service = core_app.filterModules(newService);
    },
    // 初始化订单操作数据
    initOrderTabbar() {
      const app = this;
      const newOrderNavbar = [];
      orderNavbar.forEach((item) => {
        if (item.count != void 0) {
          item.count = app.todoCounts[item.id];
        }
        newOrderNavbar.push(item);
      });
      app.orderNavbar = newOrderNavbar;
    },
    // 获取商城设置
    getSetting() {
      const app = this;
      return new Promise((resolve, reject) => {
        common_model_Setting.SettingModel.data().then((setting) => {
          app.setting = setting;
          resolve(setting);
        }).catch(reject);
      });
    },
    vip() {
      let app = this;
      if (app.userInfo.grade_id == 0) {
        api_article_index.detail(10003).then((result) => {
          app.article = result.data.detail;
          app.vipShow = true;
        });
      } else {
        common_vendor.index.navigateTo({
          url: "/pages/vip/index"
        });
      }
    },
    toVip() {
      this.vipShow = false;
      common_vendor.index.navigateTo({
        url: "/pages/vip/index"
      });
    },
    // 获取当前用户信息
    getUserInfo() {
      const app = this;
      return new Promise((resolve, reject) => {
        !app.isLogin ? resolve(null) : api_user.info({}, {
          load: app.isFirstload
        }).then((result) => {
          app.userInfo = result.data.userInfo;
          resolve(app.userInfo);
        }).catch((err) => {
          if (err.result && err.result.status == 401) {
            app.isLogin = false;
            resolve(null);
          } else {
            reject(err);
          }
        });
      });
    },
    // 获取账户资产
    getUserAssets() {
      const app = this;
      return new Promise((resolve, reject) => {
        !app.isLogin ? resolve(null) : api_user.assets({}, {
          load: app.isFirstload
        }).then((result) => {
          app.assets = result.data.assets;
          resolve(app.assets);
        }).catch((err) => {
          if (err.result && err.result.status == 401) {
            app.isLogin = false;
            resolve(null);
          } else {
            reject(err);
          }
        });
      });
    },
    // 获取当前用户待处理的订单数量
    getTodoCounts() {
      const app = this;
      return new Promise((resolve, reject) => {
        !app.isLogin ? resolve(null) : api_order.todoCounts({}, {
          load: app.isFirstload
        }).then((result) => {
          app.todoCounts = result.data.counts;
          resolve(app.todoCounts);
        }).catch((err) => {
          if (err.result && err.result.status == 401) {
            app.isLogin = false;
            resolve(null);
          } else {
            reject(err);
          }
        });
      });
    },
    // 跳转到登录页
    handleLogin() {
      if (!this.isLogin) {
        this.showLogin = true;
      }
    },
    // 跳转到绑定手机号页面
    handleBindMobile() {
      this.$navTo("pages/user/bind/index");
    },
    // 跳转到修改个人信息页
    handlePersonal() {
      this.$navTo("pages/user/personal/index");
    },
    // 退出登录
    handleLogout() {
      const app = this;
      common_vendor.index.showModal({
        title: "友情提示",
        content: "您确定要退出登录吗?",
        success(res) {
          if (res.confirm) {
            const newOrderNavbar = [];
            orderNavbar.forEach((item) => {
              item.count = 0;
              newOrderNavbar.push(item);
            });
            app.orderNavbar = newOrderNavbar;
            store_index.store.dispatch("Logout", {}).then((result) => app.onRefreshPage());
          }
        }
      });
    },
    // 跳转到钱包页面
    onTargetWallet() {
      this.$navTo("pages/wallet/index");
    },
    // 跳转到订单页
    onTargetOrder(item) {
      this.$navTo("pages/order/index", {
        dataType: item.id
      });
    },
    // 跳转到我的积分页面
    onTargetPoints() {
      this.$navTo("pages/points/log");
    },
    // 跳转到我的优惠券页
    onTargetMyCoupon() {
      this.$navTo("pages/my-coupon/index");
    },
    // 跳转到服务页面
    handleService({
      url
    }) {
      this.$navTo(url);
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.getPageData(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _component_customer_btn = common_vendor.resolveComponent("customer-btn");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  const _component_recommended = common_vendor.resolveComponent("recommended");
  const _component_PromotePopup = common_vendor.resolveComponent("PromotePopup");
  const _component_MpWeixinMobile = common_vendor.resolveComponent("MpWeixinMobile");
  (_component_avatar_image + _easycom_u_icon2 + _component_customer_btn + _easycom_mp_html2 + _easycom_u_popup2 + _component_recommended + _component_PromotePopup + _component_MpWeixinMobile)();
}
const _easycom_u_icon = () => "../../uni_modules/vk-uview-ui/components/u-icon/u-icon.js";
const _easycom_mp_html = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
const _easycom_u_popup = () => "../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_icon + _easycom_mp_html + _easycom_u_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isFirstload
  }, !$data.isFirstload ? common_vendor.e({
    b: common_assets._imports_0,
    c: $data.isLogin
  }, $data.isLogin ? common_vendor.e({
    d: common_vendor.p({
      url: $data.userInfo.avatar_url,
      width: 100
    }),
    e: common_vendor.o(($event) => $options.handlePersonal()),
    f: common_vendor.t($data.userInfo.nick_name),
    g: common_vendor.o(($event) => $options.handlePersonal()),
    h: _ctx.$checkModule("user-grade") && $data.userInfo.grade_id > 0 && $data.userInfo.grade
  }, _ctx.$checkModule("user-grade") && $data.userInfo.grade_id > 0 && $data.userInfo.grade ? {
    i: common_vendor.t($data.userInfo.grade.name)
  } : {}, {
    j: _ctx.$checkModule("user-grade") && $data.userInfo.grade_id > 0 && $data.userInfo.grade
  }, _ctx.$checkModule("user-grade") && $data.userInfo.grade_id > 0 && $data.userInfo.grade ? common_vendor.e({
    k: $data.userInfo.end_time == "永久"
  }, $data.userInfo.end_time == "永久" ? {
    l: common_vendor.t($data.userInfo.end_time)
  } : {
    m: common_vendor.t($data.userInfo.end_time)
  }, {
    n: $data.userInfo.end_time == "永久"
  }, $data.userInfo.end_time == "永久" ? {
    o: common_vendor.p({
      name: "arrow-right"
    }),
    p: common_vendor.o((...args) => $options.vip && $options.vip(...args))
  } : {
    q: common_vendor.p({
      name: "arrow-right"
    }),
    r: common_vendor.o((...args) => $options.vip && $options.vip(...args))
  }, {
    s: $data.userInfo.is_sign
  }, $data.userInfo.is_sign ? {
    t: common_vendor.o((...args) => $options.toSign && $options.toSign(...args))
  } : {
    v: common_vendor.o((...args) => $options.toSign && $options.toSign(...args))
  }) : common_vendor.e({
    w: common_vendor.o((...args) => $options.vip && $options.vip(...args)),
    x: $data.userInfo.is_sign
  }, $data.userInfo.is_sign ? {
    y: common_vendor.o((...args) => $options.toSign && $options.toSign(...args))
  } : {
    z: common_vendor.o((...args) => $options.toSign && $options.toSign(...args))
  })) : {
    A: common_vendor.p({
      width: 100
    }),
    B: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  }, {
    C: _ctx.platform == "H5" ? "260rpx" : "320rpx",
    D: _ctx.platform == "H5" ? "0" : "80rpx",
    E: $data.isLogin && !$data.userInfo.mobile && $data.setting[$data.SettingKeyEnum.REGISTER.value].isManualBind
  }, $data.isLogin && !$data.userInfo.mobile && $data.setting[$data.SettingKeyEnum.REGISTER.value].isManualBind ? {
    F: common_vendor.o(($event) => $options.handleBindMobile())
  } : {}, {
    G: _ctx.$checkModules(["market-recharge", "market-points", "market-coupon"])
  }, _ctx.$checkModules(["market-recharge", "market-points", "market-coupon"]) ? common_vendor.e({
    H: _ctx.$checkModule("market-recharge")
  }, _ctx.$checkModule("market-recharge") ? {
    I: common_vendor.t($data.isLogin ? $data.assets.balance : "--"),
    J: common_vendor.o((...args) => $options.onTargetWallet && $options.onTargetWallet(...args))
  } : {}, {
    K: _ctx.$checkModule("market-points")
  }, _ctx.$checkModule("market-points") ? {
    L: common_vendor.t($data.isLogin ? $data.assets.points : "--"),
    M: common_vendor.t($data.setting[$data.SettingKeyEnum.POINTS.value].points_name),
    N: common_vendor.o((...args) => $options.onTargetPoints && $options.onTargetPoints(...args))
  } : {}, {
    O: _ctx.$checkModule("market-coupon")
  }, _ctx.$checkModule("market-coupon") ? {
    P: common_vendor.t($data.isLogin ? $data.assets.coupon : "--"),
    Q: common_vendor.o((...args) => $options.onTargetMyCoupon && $options.onTargetMyCoupon(...args))
  } : {}, {
    R: _ctx.$checkModule("market-recharge")
  }, _ctx.$checkModule("market-recharge") ? {
    S: common_vendor.o((...args) => $options.onTargetWallet && $options.onTargetWallet(...args))
  } : {}) : {}, {
    T: common_vendor.f($data.orderNavbar, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n(`icon-${item.icon}`),
        b: common_vendor.t(item.name),
        c: item.count && item.count > 0
      }, item.count && item.count > 0 ? common_vendor.e({
        d: item.count <= 99
      }, item.count <= 99 ? {
        e: common_vendor.t(item.count)
      } : {}) : {}, {
        f: index,
        g: common_vendor.o(($event) => $options.onTargetOrder(item), index)
      });
    }),
    U: common_vendor.f($data.service, (item, index, i0) => {
      return common_vendor.e({
        a: item.type == "link" && item.enabled
      }, item.type == "link" && item.enabled ? common_vendor.e({
        b: common_vendor.n(`icon-${item.icon}`),
        c: common_vendor.t(item.name),
        d: item.count && item.count > 0
      }, item.count && item.count > 0 ? common_vendor.e({
        e: item.count <= 99
      }, item.count <= 99 ? {
        f: common_vendor.t(item.count)
      } : {}) : {}, {
        g: common_vendor.o(($event) => $options.handleService(item), index)
      }) : {}, {
        h: item.type == "contact" && item.enabled
      }, item.type == "contact" && item.enabled ? {
        i: common_vendor.n(`icon-${item.icon}`),
        j: common_vendor.t(item.name),
        k: "79e6a490-4-" + i0
      } : {}, {
        l: index
      });
    }),
    V: $data.isLogin
  }, $data.isLogin ? {
    W: common_vendor.o(($event) => $options.handleLogout())
  } : {}, {
    X: common_vendor.t($data.article.title),
    Y: common_vendor.p({
      content: $data.article.content
    }),
    Z: common_vendor.o(($event) => $data.vipShow = false),
    aa: common_vendor.o((...args) => $options.toVip && $options.toVip(...args)),
    ab: common_vendor.o(($event) => $data.vipShow = $event),
    ac: common_vendor.p({
      mode: "center",
      borderRadius: "10",
      modelValue: $data.vipShow
    }),
    ad: $data.showLogin && !$data.isLoading
  }, $data.showLogin && !$data.isLoading ? {} : {}, {
    ae: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-79e6a490"]]);
wx.createPage(MiniProgramPage);
