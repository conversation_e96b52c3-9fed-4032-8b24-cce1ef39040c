"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemStyle: Object
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: `${$props.itemStyle.lineHeight * 2}rpx ${$props.itemStyle.lineStyle} ${$props.itemStyle.lineColor}`,
    b: `${$props.itemStyle.paddingTop * 2}rpx 0`,
    c: $props.itemStyle.background
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-08dce665"]]);
wx.createComponent(Component);
