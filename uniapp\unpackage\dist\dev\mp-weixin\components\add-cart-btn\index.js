"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    // 购物车按钮样式 1 2 3
    btnStyle: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      value: false,
      goodsInfo: {}
    };
  },
  methods: {
    handleAddCart() {
      this.$emit("handleAddCart");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.n(`icon-jiagou${$props.btnStyle}`),
    b: common_vendor.o((...args) => $options.handleAddCart && $options.handleAddCart(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fcf334a9"]]);
wx.createComponent(Component);
