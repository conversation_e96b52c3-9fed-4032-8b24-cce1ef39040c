"use strict";
const common_vendor = require("../../common/vendor.js");
const common_model_Store = require("../../common/model/Store.js");
const _sfc_main = {
  emits: ["end"],
  props: {
    // 弹出隐私窗口时是否隐藏tabbar
    hideTabBar: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 隐私协议弹窗
      showPrivacy: false,
      // 商城基本信息
      storeInfo: void 0
    };
  },
  async created() {
    await this.getStoreInfo();
    this.needAuthorization();
  },
  methods: {
    // 获取商城基本信息
    async getStoreInfo() {
      await common_model_Store.StoreModel.storeInfo().then((storeInfo) => this.storeInfo = storeInfo);
    },
    // 弹出隐私协议 (微信小程序端)
    needAuthorization() {
      const app = this;
      common_vendor.index.getPrivacySetting({
        success({ needAuthorization, privacyContractName }) {
          console.info("getPrivacySetting", { needAuthorization, privacyContractName });
          if (needAuthorization) {
            app.showPrivacy = true;
            app.hideTabBar && common_vendor.index.hideTabBar();
          } else {
            app.$emit("end");
          }
        }
      });
    },
    // 查看隐私协议内容
    handlePrivacyContract() {
      common_vendor.index.openPrivacyContract();
    },
    // 用户同意隐私协议事件回调
    handleAgreePrivacyAuthorization() {
      this.hideTabBar && common_vendor.index.showTabBar();
      this.showPrivacy = false;
      this.$emit("end");
    },
    // 用户不同意隐私协议
    handleDisagree() {
      this.$toast("很抱歉，请先同意后可继续使用~", 2e3);
    }
  }
};
if (!Array) {
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  _easycom_u_popup2();
}
const _easycom_u_popup = () => "../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
if (!Math) {
  _easycom_u_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.storeInfo
  }, $data.storeInfo ? {
    b: common_vendor.t($data.storeInfo.store_name),
    c: common_vendor.o(($event) => $options.handlePrivacyContract()),
    d: common_vendor.o(($event) => $options.handleDisagree()),
    e: common_vendor.o(($event) => $options.handleAgreePrivacyAuthorization()),
    f: common_vendor.o(($event) => $data.showPrivacy = $event),
    g: common_vendor.p({
      mode: "bottom",
      ["border-radius"]: "20",
      ["safe-area-inset-bottom"]: true,
      ["mask-close-able"]: false,
      ["mask-custom-style"]: {
        background: "rgba(0, 0, 0, 0.7)"
      },
      modelValue: $data.showPrivacy
    })
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5883c152"]]);
wx.createComponent(Component);
