"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "xj.message/list",
  listDetail: "xj.message/listDetail",
  detail: "xj.message/detail",
  getMessage: "xj.message/getNew"
};
function list(param, option) {
  return utils_request_index.$http.get(api.list, param, option);
}
function detail(logId) {
  return utils_request_index.$http.get(api.detail, {
    logId
  });
}
function getMessage() {
  return utils_request_index.$http.get(api.getMessage);
}
exports.detail = detail;
exports.getMessage = getMessage;
exports.list = list;
