"use strict";
const common_vendor = require("../../../common/vendor.js");
const core_mixins_wxofficial = require("../../../core/mixins/wxofficial.js");
const core_app = require("../../../core/app.js");
require("../../../common/enum/groupon/GoodsStatus.js");
const common_enum_groupon_TaskStatus = require("../../../common/enum/groupon/TaskStatus.js");
const common_enum_groupon_ActiveType = require("../../../common/enum/groupon/ActiveType.js");
const common_enum_groupon_ActiveStatus = require("../../../common/enum/groupon/ActiveStatus.js");
const api_groupon_task = require("../../../api/groupon/task.js");
const common_model_Store = require("../../../common/model/Store.js");
const common_model_groupon_Setting = require("../../../common/model/groupon/Setting.js");
const AvatarImage = () => "../../../components/avatar-image/index.js";
const CountDown = () => "../../../components/countdown/index.js";
const Recommended = () => "../../../components/recommended/index.js";
const SkuPopup = () => "../goods/components/SkuPopup.js";
const _sfc_main = {
  components: {
    AvatarImage,
    SkuPopup,
    CountDown,
    Recommended
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 枚举类
      ActiveTypeEnum: common_enum_groupon_ActiveType.ActiveTypeEnum,
      ActiveStatusEnum: common_enum_groupon_ActiveStatus.ActiveStatusEnum,
      TaskStatusEnum: common_enum_groupon_TaskStatus.TaskStatusEnum,
      // 显示拼团规则
      showRules: false,
      // 显示/隐藏SKU弹窗
      showSkuPopup: false,
      // 按钮模式 1:都显示 2:只显示购物车 3:只显示立即购买
      skuMode: 3,
      // 当前拼单ID
      taskId: null,
      // 拼单详情
      detail: null,
      // 拼团商品
      goods: null,
      // 拼团设置
      setting: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ taskId }) {
    this.taskId = taskId;
    this.getSetting();
    this.getTaskDetail();
  },
  methods: {
    // 获取拼团设置
    getSetting() {
      common_model_groupon_Setting.GrouponSettingModel.data().then((setting) => this.setting = setting);
    },
    // 获取拼单详情
    getTaskDetail() {
      const { taskId } = this;
      this.isLoading = true;
      api_groupon_task.detail(taskId).then((result) => {
        this.detail = result.data.detail;
        this.goods = result.data.goods;
        this.setWxofficialShareData();
      }).finally(() => this.isLoading = false);
    },
    // 跳转到拼团商品详情
    onTargetGoods() {
      const { goods } = this;
      this.$navTo("pages/groupon/goods/index", { grouponGoodsId: goods.groupon_goods_id });
    },
    // 点击分享按钮
    handleShareBtn() {
    },
    // 复制当前页面链接
    handleCopyLink() {
      const app = this;
      app.getShareUrl().then((shareUrl) => {
        common_vendor.index.setClipboardData({
          data: shareUrl,
          success: () => app.$toast("复制链接成功，快去发送给朋友吧"),
          fail: ({ errMsg }) => app.$toast("复制失败 " + errMsg)
        });
      });
    },
    // 获取分享链接 (H5外链)
    getShareUrl() {
      const { path, query } = core_app.getCurrentPage();
      return new Promise((resolve, reject) => {
        common_model_Store.StoreModel.h5Url().then((baseUrl) => {
          const shareUrl = core_app.buildUrL(baseUrl, path, query);
          resolve(shareUrl);
        });
      });
    },
    // 显示拼团规则
    handleShowRules() {
      this.showRules = true;
    },
    // 显示/隐藏SKU弹窗
    onShowSkuPopup() {
      this.showSkuPopup = !this.showSkuPopup;
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const { detail, goods } = this;
      this.updateShareCardData({
        title: goods.goods_name,
        desc: goods.selling_point,
        imgUrl: goods.goods_image
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const params = app.$getShareUrlParams({ taskId: app.taskId });
    return {
      title: app.goods.goods_name,
      path: `/pages/groupon/task/index?${params}`
    };
  },
  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const app = this;
    const params = app.$getShareUrlParams({ taskId: app.taskId });
    return {
      title: app.goods.goods_name,
      path: `/pages/groupon/task/index?${params}`
    };
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _component_SkuPopup = common_vendor.resolveComponent("SkuPopup");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  const _component_recommended = common_vendor.resolveComponent("recommended");
  (_component_avatar_image + _component_count_down + _component_SkuPopup + _easycom_u_modal2 + _component_recommended)();
}
const _easycom_u_modal = () => "../../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  _easycom_u_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading && $data.detail && $data.goods
  }, !$data.isLoading && $data.detail && $data.goods ? common_vendor.e({
    b: $data.detail.active_type != $data.ActiveTypeEnum.NORMAL.value
  }, $data.detail.active_type != $data.ActiveTypeEnum.NORMAL.value ? {
    c: common_vendor.t($data.ActiveTypeEnum[$data.detail.active_type].name2)
  } : {}, {
    d: $data.goods.goods_image,
    e: common_vendor.t($data.goods.goods_name),
    f: common_vendor.t($data.goods.groupon_price),
    g: common_vendor.t($data.goods.original_price),
    h: common_vendor.t($data.detail.people),
    i: $data.goods.diff_price != "0.00"
  }, $data.goods.diff_price != "0.00" ? {
    j: common_vendor.t($data.goods.diff_price)
  } : {}, {
    k: common_vendor.o(($event) => $options.onTargetGoods()),
    l: $data.detail.status == $data.TaskStatusEnum.FAIL.value
  }, $data.detail.status == $data.TaskStatusEnum.FAIL.value ? {} : {}, {
    m: $data.detail.status == $data.TaskStatusEnum.SUCCESS.value
  }, $data.detail.status == $data.TaskStatusEnum.SUCCESS.value ? {} : {}, {
    n: common_vendor.f($data.detail.users, (item, index, i0) => {
      return common_vendor.e({
        a: "f6c73a28-0-" + i0,
        b: common_vendor.p({
          url: item.userInfo.avatar_url,
          width: 100
        }),
        c: item.is_leader
      }, item.is_leader ? {} : {}, {
        d: item.id
      });
    }),
    o: common_vendor.f($data.detail.people - $data.detail.joined_people, (val, idx, i0) => {
      return {
        a: idx
      };
    }),
    p: $data.detail.status == $data.TaskStatusEnum.NORMAL.value
  }, $data.detail.status == $data.TaskStatusEnum.NORMAL.value ? {
    q: common_vendor.t($data.detail.people - $data.detail.joined_people),
    r: common_vendor.p({
      date: $data.detail.end_time,
      separator: "colon",
      theme: "custom",
      customBgColor: "#FE5246"
    })
  } : {}, {
    s: $data.detail.status == $data.TaskStatusEnum.NORMAL.value
  }, $data.detail.status == $data.TaskStatusEnum.NORMAL.value ? common_vendor.e({
    t: !$data.detail.is_join
  }, !$data.detail.is_join ? {
    v: common_vendor.o(($event) => $options.onShowSkuPopup())
  } : {
    w: common_vendor.o(($event) => $options.handleShareBtn())
  }) : {
    x: common_vendor.o(($event) => $options.onTargetGoods())
  }, {
    y: common_vendor.t($data.setting.ruleBrief),
    z: common_vendor.o(($event) => $options.handleShowRules()),
    A: common_vendor.o(($event) => $data.showSkuPopup = $event),
    B: common_vendor.p({
      skuMode: $data.skuMode,
      goods: $data.goods,
      buyMode: 1,
      taskId: $data.detail.task_id,
      stepPeople: $data.detail.people,
      modelValue: $data.showSkuPopup
    }),
    C: common_vendor.t($data.setting.ruleDetail),
    D: common_vendor.o(($event) => $data.showRules = $event),
    E: common_vendor.p({
      title: "拼团规则",
      modelValue: $data.showRules
    }),
    F: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f6c73a28"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
