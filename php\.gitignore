# Composer依赖包
composer.phar

# 运行时文件和缓存
/runtime/
/runtime/*

# 日志文件
*.log
/runtime/log/
/runtime/cache/

# 临时文件
/public/temp/
/public/uploads/
temp/
tmp/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
/config/database.php
/config/cache.php
/app/*/config/
.env
.env.local
.env.*.local

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Windows系统文件
desktop.ini
$RECYCLE.BIN/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 用户上传的文件
/public/uploads/*
!/public/uploads/.gitkeep

# 下载文件
/public/downloads/*
!/public/downloads/.gitkeep

# 支付相关敏感文件
/data/payment/

# 安装文件（生产环境不需要）
/public/install/

# 验证文件（临时文件）
/public/verify_*.html
/public/*Notice.php

# Node.js相关（如果有前端构建）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他项目特定文件
/uni/
/potatoes---heart-php/

# 锁文件（可选，团队开发建议保留）
# composer.lock

# 版本文件
version.json
/.htaccess
