"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  __name: "cy-qiandao",
  props: ["qiandaoinfo"],
  emits: ["qiandaodays", "clickday"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const nowdate = /* @__PURE__ */ new Date();
    const year = common_vendor.ref(nowdate.getFullYear());
    const month = common_vendor.ref(nowdate.getMonth());
    const weekdate = common_vendor.ref();
    const nowlastday = common_vendor.ref();
    const day = common_vendor.ref(nowdate.getDate());
    const nownyr = common_vendor.ref("");
    const nowdays = common_vendor.ref([]);
    const nextdays = common_vendor.ref([]);
    const updays = common_vendor.ref([]);
    const isclickday = common_vendor.ref("");
    const daindaoarr = common_vendor.ref([]);
    const weeks = common_vendor.ref([
      { id: 0, name: "日" },
      { id: 1, name: "一" },
      { id: 2, name: "二" },
      { id: 3, name: "三" },
      { id: 4, name: "四" },
      { id: 5, name: "五" },
      { id: 6, name: "六" }
    ]);
    common_vendor.onMounted(() => {
      initqiandao();
      getnownyr();
    });
    const emit = __emit;
    function initqiandao() {
      daindaoarr.value = props.qiandaoinfo;
      huoqulastday();
    }
    function getnownyr() {
      let date = /* @__PURE__ */ new Date();
      let nian = date.getFullYear();
      let yue = date.getMonth();
      let ri = date.getDate();
      let nyr = nian + "-" + (yue + 1) + "-" + ri;
      nownyr.value = nyr;
    }
    function nowqiandao() {
      let index = daindaoarr.value.indexOf(nownyr.value);
      if (index == -1) {
        daindaoarr.value.push(nownyr.value);
        huoqulastday();
        common_vendor.index.showToast({
          title: "签到成功"
        });
      } else {
        common_vendor.index.showToast({
          title: "已签到"
        });
      }
      let arr = nownyr.value.split("-");
      let info = {
        nyrinfo: { nian: arr[0], yue: arr[1], day: arr[2], nyr: nownyr.value },
        qiandaoarr: daindaoarr.value
      };
      emit("qiandaodays", info);
    }
    function clickri(item) {
      if (item.nyr == nownyr.value) {
        nowqiandao();
      }
      isclickday.value = item.nyr;
      let info = {
        nyrinfo: { nian: item.nian, yue: item.yue, day: item.days, nyr: item.nyr },
        qiandaoarr: daindaoarr.value
      };
      emit("clickday", info);
    }
    function upmonth() {
      if (month.value <= 0) {
        month.value = 11;
        year.value = year.value - 1;
      } else {
        month.value = month.value - 1;
      }
      huoqulastday();
    }
    function downmonth() {
      if (month.value >= 11) {
        month.value = 0;
        year.value = year.value + 1;
      } else {
        month.value = month.value + 1;
      }
      huoqulastday();
    }
    function tojinri() {
      const nowdate1 = /* @__PURE__ */ new Date();
      year.value = nowdate1.getFullYear();
      month.value = nowdate1.getMonth();
      huoqulastday();
    }
    function huoqulastday() {
      const nian = year.value;
      const yue = month.value;
      day.value;
      nowlastday.value = new Date(nian, yue + 1, 0).getDate();
      const daynum = [];
      for (let i = 1; i <= nowlastday.value; i++) {
        let nyr = nian + "-" + (yue + 1) + "-" + i;
        let index = daindaoarr.value.indexOf(nyr);
        let qiandao = false;
        if (index != -1) {
          qiandao = true;
        }
        daynum.push({ isqiandao: qiandao, nian, yue: yue + 1, days: i, nyr: nian + "-" + (yue + 1) + "-" + i });
      }
      nowdays.value = daynum;
      weekdate.value = new Date(nian, yue, 1).getDay();
      let upzongdays = new Date(nian, yue, 0).getDate();
      let upnums = upzongdays - weekdate.value + 1;
      if (weekdate.value == 0) {
        updays.value = [];
      }
      const upnuminfo = [];
      let nian0 = "";
      let yue0 = "";
      if (yue == 0) {
        nian0 = nian - 1;
        yue0 = 12;
      } else {
        yue0 = yue;
        nian0 = nian;
      }
      for (let i = upnums; i <= upzongdays; i++) {
        let nyr = nian0 + "-" + yue0 + "-" + i;
        let index = daindaoarr.value.indexOf(nyr);
        let qiandao = false;
        if (index != -1) {
          qiandao = true;
        }
        upnuminfo.push({ isqiandao: qiandao, nian: nian0, yue: yue0, days: i, nyr: nian0 + "-" + yue0 + "-" + i });
      }
      updays.value = upnuminfo;
      let nowlastdays1 = new Date(nian, yue, nowlastday.value).getDay();
      let lastnum = 6 - nowlastdays1;
      let lastdays = [];
      let yue1 = "";
      let nian1 = "";
      if (yue == 11) {
        yue1 = 1;
        nian1 = nian + 1;
      } else {
        yue1 = yue + 2;
        nian1 = nian;
      }
      for (let i = 1; i <= lastnum; i++) {
        let nyr = nian1 + "-" + yue1 + "-" + i;
        let index = daindaoarr.value.indexOf(nyr);
        let qiandao = false;
        if (index != -1) {
          qiandao = true;
        }
        lastdays.push({ isqiandao: qiandao, nian: nian1, yue: yue1, days: i, nyr: nian1 + "-" + yue1 + "-" + i });
      }
      nextdays.value = lastdays;
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(nowqiandao),
        b: common_vendor.o(upmonth),
        c: common_vendor.t(year.value),
        d: common_vendor.t(month.value + 1),
        e: common_vendor.o(downmonth),
        f: common_vendor.o(tojinri),
        g: common_vendor.f(weeks.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index
          };
        }),
        h: common_vendor.f(updays.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.days),
            b: common_vendor.n(isclickday.value == item.nyr ? "isactive" : ""),
            c: common_vendor.n(item.isqiandao ? "isqiandao" : ""),
            d: common_vendor.o(($event) => clickri(item), index),
            e: index
          };
        }),
        i: common_vendor.f(nowdays.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.days),
            b: common_vendor.n(item.nyr == nownyr.value ? "jinri" : ""),
            c: common_vendor.n(isclickday.value == item.nyr ? "isactive" : ""),
            d: common_vendor.n(item.isqiandao ? "isqiandao" : ""),
            e: common_vendor.o(($event) => clickri(item), index),
            f: index
          };
        }),
        j: common_vendor.f(nextdays.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.days),
            b: common_vendor.n(isclickday.value == item.nyr ? "isactive" : ""),
            c: common_vendor.n(item.isqiandao ? "isqiandao" : ""),
            d: common_vendor.o(($event) => clickri(item), index),
            e: index
          };
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-30818bab"]]);
wx.createComponent(Component);
