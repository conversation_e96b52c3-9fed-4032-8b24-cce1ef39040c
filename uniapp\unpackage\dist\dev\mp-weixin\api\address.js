"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "address/list",
  defaultId: "address/defaultId",
  detail: "address/detail",
  add: "address/add",
  edit: "address/edit",
  setDefault: "address/setDefault",
  remove: "address/remove",
  analysis: "address/analysis"
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
const defaultId = (param) => {
  return utils_request_index.$http.get(api.defaultId, param);
};
const detail = (addressId) => {
  return utils_request_index.$http.get(api.detail, { addressId });
};
const add = (data) => {
  return utils_request_index.$http.post(api.add, { form: data });
};
const edit = (addressId, data) => {
  return utils_request_index.$http.post(api.edit, { addressId, form: data });
};
const setDefault = (addressId) => {
  return utils_request_index.$http.post(api.setDefault, { addressId });
};
const remove = (addressId) => {
  return utils_request_index.$http.post(api.remove, { addressId });
};
const analysis = (content) => {
  return utils_request_index.$http.get(api.analysis, { content });
};
exports.add = add;
exports.analysis = analysis;
exports.defaultId = defaultId;
exports.detail = detail;
exports.edit = edit;
exports.list = list;
exports.remove = remove;
exports.setDefault = setDefault;
