"use strict";
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
const api_refund = require("../../api/refund.js");
const common_vendor = require("../../common/vendor.js");
const pageSize = 15;
const tabs = [{
  name: "全部",
  value: -1
}, {
  name: "待处理",
  value: 0
}];
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 订单列表数据
      list: core_app.getEmptyPaginateObj(),
      // tabs栏数据
      tabs,
      // 当前标签索引
      curTab: 0,
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于2条才显示无更多数据
        noMoreSize: 2,
        // 空布局
        empty: {
          tip: "亲，暂无售后单记录"
        }
      },
      // 控制首次触发onShow事件时不刷新列表
      canReset: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.canReset && this.onRefreshList();
    this.canReset = true;
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getRefundList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取退款/售后单列表
    getRefundList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_refund.list({ state: app.getTabValue(), page: pageNo }, { load: false }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    },
    // 切换标签项
    onChangeTab(index) {
      const app = this;
      app.curTab = index;
      app.onRefreshList();
    },
    // 刷新订单列表
    onRefreshList() {
      this.list = core_app.getEmptyPaginateObj();
      setTimeout(() => {
        this.mescroll.resetUpScroll();
      }, 120);
    },
    // 获取当前标签项的值
    getTabValue() {
      return this.tabs[this.curTab].value;
    },
    // 跳转到售后单详情页
    handleTargetDetail(orderRefundId, type) {
      this.$navTo("pages/refund/detail", { orderRefundId, type });
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_easycom_u_tabs2 + _easycom_mescroll_body2)();
}
const _easycom_u_tabs = () => "../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_mescroll_body)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabs,
      ["is-scroll"]: false,
      ["active-color"]: _ctx.appTheme.mainBg,
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.create_time),
        b: common_vendor.t(item.state_text),
        c: item.orderGoods.goods_image,
        d: common_vendor.t(item.orderGoods.goods_name),
        e: common_vendor.f(item.orderGoods.goods_props, (props, idx, i1) => {
          return {
            a: common_vendor.t(props.value.name),
            b: idx
          };
        }),
        f: common_vendor.t(item.orderGoods.total_num),
        g: common_vendor.o(($event) => $options.handleTargetDetail(item.order_refund_id, item.order_type), index),
        h: item.order_type == 2
      }, item.order_type == 2 ? {
        i: common_vendor.t(Number(item.orderGoods.total_pay_price))
      } : {
        j: common_vendor.t(item.orderGoods.total_pay_price)
      }, {
        k: common_vendor.o(($event) => $options.handleTargetDetail(item.order_refund_id, item.order_type), index),
        l: index
      });
    }),
    e: common_vendor.sr("mescrollRef", "f8fecd22-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o(_ctx.downCallback),
    h: common_vendor.o($options.upCallback),
    i: common_vendor.p({
      sticky: true,
      down: {
        native: true
      },
      up: $data.upOption
    }),
    j: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f8fecd22"]]);
wx.createPage(MiniProgramPage);
