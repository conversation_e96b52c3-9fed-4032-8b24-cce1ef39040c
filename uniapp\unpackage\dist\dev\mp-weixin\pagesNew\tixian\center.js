"use strict";
const common_vendor = require("../../common/vendor.js");
const api_page = require("../../api/page.js");
const Page = () => "../../components/page/index.js";
const _sfc_main = {
  components: {
    Page
  },
  data() {
    return {
      // 页面参数
      options: {},
      // 页面属性
      page: {},
      // 页面元素
      items: []
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    this.getPageData();
  },
  methods: {
    /**
     * 加载页面数据
     * @param {Object} callback
     */
    getPageData(callback) {
      const app = this;
      const pageId = app.options.pageId || 10008;
      api_page.detail(pageId).then((result) => {
        const { data: { pageData } } = result;
        app.page = pageData.page;
        app.items = pageData.items;
        app.setPageBar();
      }).finally(() => callback && callback());
    },
    /**
     * 设置顶部导航栏
     */
    setPageBar() {
      const { page } = this;
      common_vendor.index.setNavigationBarTitle({
        title: page.params.title
      });
      common_vendor.index.setNavigationBarColor({
        frontColor: page.style.titleTextColor === "white" ? "#ffffff" : "#000000",
        backgroundColor: page.style.titleBackgroundColor
      });
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.getPageData(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const { page } = app;
    return {
      title: page.params.shareTitle,
      path: "/pages/custom/index?" + app.$getShareUrlParams({ pageId: app.options.pageId })
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const { page } = app;
    return {
      title: page.params.shareTitle,
      path: "/pages/custom/index?" + app.$getShareUrlParams({ pageId: app.options.pageId })
    };
  }
};
if (!Array) {
  const _component_Page = common_vendor.resolveComponent("Page");
  _component_Page();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      items: $data.items
    }),
    b: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5316e1a2"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
