<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores;

// 应用请求对象类
class Request extends \think\Request
{
    // 全局过滤规则
    protected $filter = ['my_trim', 'my_htmlspecialchars', 'filter_emoji'];

    // 当前的商城ID (仅在访问store模块和api模块时有值)
    protected ?int $storeId = null;

    /**
     * 获取当前的商城ID (仅在访问store模块和api模块时有值)
     * @return int
     */
    public function storeId(): int
    {
        return (int)$this->storeId;
    }

    /**
     * 设置当前商城ID
     * @param int $storeId
     * @return $this
     */
    public function setStoreId(int $storeId): Request
    {
        $this->storeId = $storeId;
        return $this;
    }
}
