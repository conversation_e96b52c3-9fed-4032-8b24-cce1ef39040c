"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../store/index.js");
const api_user = require("../../../api/user.js");
const api_captcha = require("../../../api/captcha.js");
const utils_verify = require("../../../utils/verify.js");
const times = 60;
const GET_CAPTCHA = 10;
const FORM_SUBMIT = 20;
const _sfc_main = {
  data() {
    return {
      // 正在加载
      isLoading: false,
      // 图形验证码信息
      captcha: {},
      // 短信验证码发送状态
      smsState: false,
      // 倒计时
      times,
      // 手机号
      mobile: "",
      // 图形验证码
      captchaCode: "",
      // 短信验证码
      smsCode: ""
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  created() {
    this.getCaptcha();
  },
  methods: {
    // 获取图形验证码
    getCaptcha() {
      const app = this;
      api_captcha.image().then((result) => app.captcha = result.data);
    },
    // 点击发送短信验证码
    handelSmsCaptcha() {
      const app = this;
      if (!app.isLoading && !app.smsState && app.formValidation(GET_CAPTCHA)) {
        app.sendSmsCaptcha();
        app.getCaptcha();
      }
    },
    // 表单验证
    formValidation(scene = GET_CAPTCHA) {
      const app = this;
      if (scene === GET_CAPTCHA) {
        if (!app.validteMobile(app.mobile) || !app.validteCaptchaCode(app.captchaCode)) {
          return false;
        }
      }
      if (scene === FORM_SUBMIT) {
        if (!app.validteMobile(app.mobile) || !app.validteSmsCode(app.smsCode)) {
          return false;
        }
      }
      return true;
    },
    // 验证手机号
    validteMobile(str) {
      if (utils_verify.isEmpty(str)) {
        this.$toast("请先输入手机号");
        return false;
      }
      if (!utils_verify.isMobile(str)) {
        this.$toast("请输入正确格式的手机号");
        return false;
      }
      return true;
    },
    // 验证图形验证码
    validteCaptchaCode(str) {
      if (utils_verify.isEmpty(str)) {
        this.$toast("请先输入图形验证码");
        return false;
      }
      return true;
    },
    // 验证短信验证码
    validteSmsCode(str) {
      if (utils_verify.isEmpty(str)) {
        this.$toast("请先输入短信验证码");
        return false;
      }
      return true;
    },
    // 请求发送短信验证码接口
    sendSmsCaptcha() {
      const app = this;
      app.isLoading = true;
      api_captcha.sendSmsCaptcha({
        form: {
          captchaKey: app.captcha.key,
          captchaCode: app.captchaCode,
          mobile: app.mobile
        }
      }).then((result) => {
        app.$toast(result.message);
        app.timer();
      }).finally(() => app.isLoading = false);
    },
    // 执行定时器
    timer() {
      const app = this;
      app.smsState = true;
      const inter = setInterval(() => {
        app.times = app.times - 1;
        if (app.times <= 0) {
          app.smsState = false;
          app.times = times;
          clearInterval(inter);
        }
      }, 1e3);
    },
    // 点击提交
    handleSubmit() {
      const app = this;
      if (!app.isLoading && app.formValidation(FORM_SUBMIT)) {
        app.onSubmitEvent();
      }
    },
    // 确认提交事件
    onSubmitEvent() {
      const app = this;
      app.isLoading = true;
      api_user.bindMobile({ form: { smsCode: app.smsCode, mobile: app.mobile } }).then((result) => {
        app.$toast(result.message);
        setTimeout(() => {
          app.onNavigateBack(1);
        }, 2e3);
      }).finally(() => app.isLoading = false);
    },
    /**
     * 提交成功-跳转回原页面
     */
    onNavigateBack(delta) {
      common_vendor.index.navigateBack({
        delta: Number(delta || 1)
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.mobile,
    b: common_vendor.o(($event) => $data.mobile = $event.detail.value),
    c: $data.captchaCode,
    d: common_vendor.o(($event) => $data.captchaCode = $event.detail.value),
    e: $data.captcha.base64,
    f: common_vendor.o(($event) => $options.getCaptcha()),
    g: $data.smsCode,
    h: common_vendor.o(($event) => $data.smsCode = $event.detail.value),
    i: !$data.smsState
  }, !$data.smsState ? {} : {
    j: common_vendor.t($data.times)
  }, {
    k: common_vendor.o(($event) => $options.handelSmsCaptcha()),
    l: common_vendor.o(($event) => $options.handleSubmit()),
    m: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c3b8e2dd"]]);
wx.createPage(MiniProgramPage);
