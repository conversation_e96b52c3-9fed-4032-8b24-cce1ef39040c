<?php

declare (strict_types = 1);

namespace app\api\model\xj;

use app\api\service\User as UserService;
use app\common\model\User as UserModel;
use cores\BaseModel;
use cores\exception\BaseException;
use think\facade\Db;

/**
 * 商品兑换模型
 * Class Exchange
 * @package app\api\model\xj
 */
class Exchange extends BaseModel
{
    // 定义表名
    protected $name = 'xj_exchange';

    // 定义主键
    protected $pk = 'exchange_id';

    // 商品配置
    const PRODUCTS = [
        '10007' => [
            'goods_id' => 10007,
            'name' => '有机旱田雪花粉',
            'required_sign_days' => 1,
            'required_points' => 2,
            'max_exchange_count' => 1
        ],
        '10008' => [
            'goods_id' => 10008,
            'name' => '有机福禄喜米',
            'required_sign_days' => 14,
            'required_points' => 1200,
            'max_exchange_count' => 1
        ]
    ];

    /**
     * 获取兑换页面数据
     * @return array
     * @throws BaseException
     */
    public function getExchangeData(): array
    {
        // 当前用户信息
        $userInfo = UserService::getCurrentLoginUser(true);
        $userId = $userInfo['user_id'];

        // 获取用户积分
        $userPoints = $userInfo['points'];

        // 获取已兑换的商品ID列表
        $exchangedProducts = Db::name('xj_exchange')
            ->where('user_id', $userId)
            ->where('store_id', self::$storeId)
            ->column('product_id');

        return [
            'user_points' => $userPoints,
            'exchanged_products' => $exchangedProducts
        ];
    }

    /**
     * 兑换商品
     * @param array $data
     * @return bool
     * @throws BaseException
     */
    public function exchangeProduct(array $data): bool
    {
        $productId = $data['product_id'];

        // 检查商品配置
        if (!isset(self::PRODUCTS[$productId])) {
            $this->error = '无效的商品';
            return false;
        }

        $config = self::PRODUCTS[$productId];

        // 当前用户信息
        $userInfo = UserService::getCurrentLoginUser(true);
        $userId = $userInfo['user_id'];

        // 检查是否已经兑换过
        if ($this->hasExchangedProduct($userId, $productId)) {
            $this->error = '您已经兑换过' . $config['name'] . '，每人限兑1份';
            return false;
        }

        // 检查积分
        if ($userInfo['points'] < $config['required_points']) {
            $this->error = '积分不足，需要' . $config['required_points'] . '积分';
            return false;
        }

        // 检查商品兑换数量限制
        $exchangedCount = $this->where('product_id', $productId)
            ->where('store_id', self::$storeId)
            ->count();
        if ($exchangedCount >= $config['max_exchange_count']) {
            $this->error = $config['name'] . '兑换数量已达上限，请关注后续活动';
            return false;
        }

        // 开始事务
        Db::startTrans();
        try {
            // 扣除积分
            UserModel::setDecPoints($userId, $config['required_points'], '兑换' . $config['name']);

            // 创建订单
            $orderId = $this->createOrder($userId, $productId, $data);

            // 记录兑换日志
            $this->save([
                'user_id' => $userId,
                'product_id' => $productId,
                'order_id' => $orderId,
                'points_used' => $config['required_points'],
                'name' => $data['name'],
                'phone' => $data['phone'],
                'address' => $data['address'],
                'store_id' => self::$storeId,
                'create_time' => time()
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            $this->error = '兑换失败：' . $e->getMessage();
            return false;
        }
    }



    /**
     * 创建订单
     * @param int $userId
     * @param int $productId
     * @param array $data
     * @return int
     */
    private function createOrder(int $userId, int $productId, array $data)
    {
        $config = self::PRODUCTS[$productId];

        $orderData = [
            'user_id' => $userId,
            'order_no' => 'EX' . date('YmdHis') . rand(1000, 9999),
            'total_price' => 0.00,
            'pay_price' => 0.00,
            'pay_status' => 20, // 已支付
            'delivery_status' => 10, // 待发货
            'order_status' => 10, // 进行中
            'receipt_status' => 10, // 待收货
            'order_source' => 30, // 积分兑换
            'store_id' => self::$storeId,
            'create_time' => time(),
            'update_time' => time()
        ];

        $orderId = (int)Db::name('order')->insertGetId($orderData);

        // 获取商品基本信息
        $goodsInfo = Db::name('goods')->where('goods_id', $productId)->find();
        if (!$goodsInfo) {
            throw new BaseException('商品不存在');
        }

        // 获取商品主图
        $imageId = Db::name('goods_image')->where('goods_id', $productId)->value('image_id') ?: 0;

        // 创建订单商品记录
        Db::name('order_goods')->insert([
            'order_id' => $orderId,
            'goods_id' => $productId,
            'goods_name' => $goodsInfo['goods_name'],
            'image_id' => $imageId,
            'content' => $goodsInfo['content'] ?: '积分兑换商品',
            'goods_price' => $goodsInfo['goods_price_min'],
            'line_price' => $goodsInfo['line_price_min'],
            'total_num' => 1,
            'total_price' => $goodsInfo['goods_price_min'],
            'total_pay_price' => 0.00,
            'user_id' => $userId,
            'store_id' => self::$storeId,
            'create_time' => time()
        ]);

        // 创建订单地址记录
        Db::name('order_address')->insert([
            'order_id' => $orderId,
            'name' => $data['name'],
            'phone' => $data['phone'],
            'detail' => $data['address'],
            'store_id' => self::$storeId,
            'create_time' => time()
        ]);

        return $orderId;
    }

    /**
     * 检查用户是否已经兑换过指定商品
     * @param int $userId
     * @param int $productId
     * @return bool
     */
    private function hasExchangedProduct(int $userId, int $productId): bool
    {
        return $this->where('user_id', $userId)
            ->where('product_id', $productId)
            ->where('store_id', self::$storeId)
            ->count() > 0;
    }
}
