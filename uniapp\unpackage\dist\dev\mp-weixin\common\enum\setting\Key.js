"use strict";
const common_enum_enum = require("../enum.js");
const SettingKeyEnum = new common_enum_enum.Enum([
  {
    key: "REGISTER",
    name: "账户注册设置",
    value: "register"
  },
  {
    key: "APP_THEME",
    name: "店铺页面风格",
    value: "app_theme"
  },
  {
    key: "PAGE_CATEGORY_TEMPLATE",
    name: "分类页模板",
    value: "page_category_template"
  },
  {
    key: "POINTS",
    name: "积分设置",
    value: "points"
  },
  {
    key: "RECHARGE",
    name: "充值设置",
    value: "recharge"
  },
  {
    key: "RECOMMENDED",
    name: "商品推荐设置",
    value: "recommended"
  },
  {
    key: "CUSTOMER",
    name: "商城客服设置",
    value: "customer"
  }
]);
exports.SettingKeyEnum = SettingKeyEnum;
