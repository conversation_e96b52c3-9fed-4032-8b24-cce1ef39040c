"use strict";
const utils_request_core_utils = require("./utils.js");
class request {
  constructor(options) {
    this.baseUrl = options.baseUrl || "";
    this.fileUrl = options.fileUrl || "";
    this.timeout = options.timeout || 6e3;
    this.defaultUploadUrl = options.defaultUploadUrl || "";
    this.header = options.header || {};
    this.config = options.config || {
      isPrompt: true,
      load: true,
      isFactory: true,
      resend: 0
    };
  }
  // post请求
  post(url = "", data = {}, options = {}) {
    return this.request({
      method: "POST",
      data,
      url,
      ...options
    });
  }
  // get请求
  get(url = "", data = {}, options = {}) {
    return this.request({
      method: "GET",
      data,
      url,
      ...options
    });
  }
  // put请求
  put(url = "", data = {}, options = {}) {
    return this.request({
      method: "PUT",
      data,
      url,
      ...options
    });
  }
  // delete请求
  delete(url = "", data = {}, options = {}) {
    return this.request({
      method: "DELETE",
      data,
      url,
      ...options
    });
  }
  // jsonp请求(只限于H5使用)
  jsonp(url = "", data = {}, options = {}) {
    return this.request({
      method: "JSONP",
      data,
      url,
      ...options
    });
  }
  // 接口请求方法
  async request(data) {
    let requestInfo, runRequestStart = false;
    try {
      if (!data.url) {
        throw {
          errMsg: "【request】缺失数据url",
          statusCode: 0
        };
      }
      requestInfo = utils_request_core_utils.mergeConfig(this, data);
      runRequestStart = true;
      if (this.requestStart) {
        let requestStart = this.requestStart(requestInfo);
        if (typeof requestStart == "object") {
          let changekeys = ["data", "header", "isPrompt", "load", "isFactory"];
          changekeys.forEach((key) => {
            requestInfo[key] = requestStart[key];
          });
        } else {
          throw {
            errMsg: "【request】请求开始拦截器未通过",
            statusCode: 0,
            data: requestInfo.data,
            method: requestInfo.method,
            header: requestInfo.header,
            url: requestInfo.url
          };
        }
      }
      let requestResult = {};
      if (requestInfo.method == "JSONP") {
        requestResult = await utils_request_core_utils.jsonpRequest(requestInfo);
      } else {
        requestResult = await utils_request_core_utils.dispatchRequest(requestInfo);
      }
      if (requestInfo.isFactory && this.dataFactory) {
        let result = await this.dataFactory({
          ...requestInfo,
          response: requestResult
        });
        return Promise.resolve(result);
      } else {
        return Promise.resolve(requestResult);
      }
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    } finally {
      if (runRequestStart) {
        this.requestEnd && this.requestEnd(requestInfo);
      }
    }
  }
}
exports.request = request;
