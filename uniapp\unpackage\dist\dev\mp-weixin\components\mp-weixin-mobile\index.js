"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const common_model_Store = require("../../common/model/Store.js");
const _sfc_main = {
  props: {
    // 是否存在第三方用户信息
    isParty: {
      type: Boolean,
      default: () => false
    },
    showLogin: {
      type: Boolean,
      default: () => false
    },
    // 第三方用户信息数据
    partyData: {
      type: Object
    }
  },
  data() {
    return {
      showTips: false,
      animation: "",
      checked: false,
      storeInfo: {
        image_url: "",
        store_name: ""
      },
      // 微信小程序登录凭证 (code)
      // 提交到后端，用于换取openid
      code: ""
    };
  },
  created() {
    this.getStoreInfo();
  },
  methods: {
    toDetail(articleId) {
      common_vendor.index.navigateTo({
        url: "/pages/article/xieyi?articleId=" + articleId
      });
    },
    sidebarClick() {
      if (this.checked == true) {
        return;
      }
      this.animation = "shake";
      setTimeout(() => {
        this.animation = "";
        this.showTips = true;
      }, 1e3);
    },
    tiaoguo() {
      console.log(77);
      common_vendor.index.$emit("event", false);
      this.showTips = false;
    },
    xieyi() {
      this.checked = this.checked == true ? false : true;
      this.showTips = false;
    },
    // 获取商城基本信息
    getStoreInfo() {
      common_model_Store.StoreModel.storeInfo().then((storeInfo) => this.storeInfo = storeInfo);
    },
    // 按钮点击事件: 获取微信手机号按钮
    // 实现目的: 在getphonenumber事件触发之前获取微信登录code
    // 因为如果在getphonenumber事件中获取code的话,提交到后端的encryptedData会存在解密不了的情况
    async clickPhoneNumber() {
      this.code = await this.getCode();
    },
    // 微信授权获取手机号一键登录
    // getphonenumber事件的回调方法
    async handelMpWeixinMobileLogin({
      detail
    }) {
      const app = this;
      if (detail.errMsg != "getPhoneNumber:ok") {
        console.log("微信授权获取手机号失败", detail);
        return;
      }
      if (detail.errMsg == "getPhoneNumber:ok") {
        app.isLoading = true;
        store_index.store.dispatch("LoginMpWxMobile", {
          code: app.code,
          encryptedData: detail.encryptedData,
          iv: detail.iv,
          isParty: true,
          partyData: {
            code: await app.getCode(),
            oauth: "MP-WEIXIN"
          },
          refereeId: store_index.store.getters.refereeId
        }).then((result) => {
          app.$toast(result.message);
          common_vendor.index.setStorageSync("clerkId", result.data.clerkId);
          common_vendor.index.setStorageSync("role", result.data.role);
          common_vendor.index.$emit("syncRefresh", true);
          common_vendor.index.$emit("event", false);
          common_vendor.index.$emit("login", true);
        }).catch((err) => {
          err.result.data;
        }).finally(() => app.isLoading = false);
      }
    },
    // 获取微信登录的code
    // https://developers.weixin.qq.com/miniprogram/dev/api/open-api/login/wx.login.html
    getCode() {
      return new Promise((resolve, reject) => {
        common_vendor.index.login({
          provider: "weixin",
          success: (res) => {
            console.log("code", res.code);
            resolve(res.code);
          },
          fail: reject
        });
      });
    },
    /**
     * 登录成功-跳转回原页面
     */
    onNavigateBack(delta = 1) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: Number(delta || 1)
        });
      } else {
        this.$navTo("pages/index/index");
      }
    }
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_mask2 = common_vendor.resolveComponent("u-mask");
  (_easycom_u_icon2 + _easycom_u_mask2)();
}
const _easycom_u_icon = () => "../../uni_modules/vk-uview-ui/components/u-icon/u-icon.js";
const _easycom_u_mask = () => "../../uni_modules/vk-uview-ui/components/u-mask/u-mask.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_mask)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.storeInfo.image_url,
    b: common_vendor.t($data.storeInfo.store_name),
    c: common_vendor.o((...args) => $options.sidebarClick && $options.sidebarClick(...args)),
    d: [$data.checked == true ? "getPhoneNumber" : ""],
    e: common_vendor.o(($event) => $options.handelMpWeixinMobileLogin($event)),
    f: common_vendor.o((...args) => $options.clickPhoneNumber && $options.clickPhoneNumber(...args)),
    g: common_vendor.o((...args) => $options.tiaoguo && $options.tiaoguo(...args)),
    h: $data.checked == true
  }, $data.checked == true ? {
    i: common_vendor.p({
      name: "checkbox-mark",
      size: "22"
    })
  } : {}, {
    j: common_vendor.n($data.checked == true ? "active" : ""),
    k: common_vendor.o((...args) => $options.xieyi && $options.xieyi(...args)),
    l: common_vendor.o((...args) => $options.xieyi && $options.xieyi(...args)),
    m: common_vendor.o(($event) => $options.toDetail(1)),
    n: common_vendor.o(($event) => $options.toDetail(2)),
    o: common_vendor.n("animation-" + $data.animation),
    p: $data.showTips
  }, $data.showTips ? {
    q: common_vendor.p({
      name: "arrow-down-fill",
      color: "#666666"
    }),
    r: common_vendor.o((...args) => $options.xieyi && $options.xieyi(...args))
  } : {}, {
    s: common_vendor.p({
      show: "true",
      ["z-index"]: "998"
    })
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1f197b9c"]]);
wx.createComponent(Component);
