"use strict";
const api_region = require("../../api/region.js");
const utils_storage = require("../../utils/storage.js");
const common_data_region = require("../data/region.js");
const REGION_TREE = "region_tree";
const RegionModel = {
  // 从服务端获取全部地区数据(树状)
  getTreeDataFromApi() {
    return new Promise((resolve, reject) => {
      api_region.tree().then((result) => resolve(result.data.list));
    });
  },
  // 获取所有地区(树状) 从storage中获取
  getTreeData() {
    return new Promise((resolve, reject) => {
      const data = utils_storage.storage.get(REGION_TREE);
      if (data) {
        resolve(data);
      } else {
        this.getTreeDataFromApi().then((list) => {
          utils_storage.storage.set(REGION_TREE, list, 24 * 60 * 60);
          resolve(list);
        });
      }
    });
  },
  // 同步获取所有地区(树状)
  getTreeDataSync() {
    return common_data_region.regionData;
  },
  // 获取所有地区的总数
  getCitysCount() {
    return new Promise((resolve, reject) => {
      this.getTreeData().then((data) => {
        const cityIds = [];
        for (const pidx in data) {
          const province = data[pidx];
          for (const cidx in province.city) {
            const cityItem = province.city[cidx];
            cityIds.push(cityItem.id);
          }
        }
        resolve(cityIds.length);
      });
    });
  },
  // 同步获取所有地区的总数
  getCitysCountSync() {
    const regionData = this.getTreeDataSync();
    const cityIds = [];
    for (const pidx in regionData) {
      const province = regionData[pidx];
      for (const cidx in province.city) {
        const cityItem = province.city[cidx];
        cityIds.push(cityItem.id);
      }
    }
    return cityIds.length;
  }
};
exports.RegionModel = RegionModel;
