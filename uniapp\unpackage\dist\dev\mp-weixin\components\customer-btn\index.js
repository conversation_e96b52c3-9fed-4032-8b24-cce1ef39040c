"use strict";
const common_vendor = require("../../common/vendor.js");
const common_enum_setting_Key = require("../../common/enum/setting/Key.js");
const common_model_Setting = require("../../common/model/Setting.js");
require("../../store/index.js");
require("../../utils/request/index.js");
const _sfc_main = {
  props: {
    // 是否显示消息卡片
    showCard: {
      Type: Boolean,
      default: false
    },
    // 消息卡片标题
    cardTitle: {
      Type: String,
      default: ""
    },
    // 消息卡片图片
    cardImage: {
      Type: String,
      default: ""
    },
    // 消息卡片点击跳转的路径
    cardPath: {
      Type: String,
      default: ""
    }
  },
  data() {
    return {
      isShow: false,
      setting: {}
    };
  },
  async created() {
    this.isShow = await common_model_Setting.SettingModel.isShowCustomerBtn();
    this.setting = await common_model_Setting.SettingModel.item(common_enum_setting_Key.SettingKeyEnum.CUSTOMER.value, true);
  },
  methods: {
    // 在线客服 (企业微信客服)
    handleContact() {
      const app = this;
      const { setting } = app;
      if (setting.provider == "wxqykf") {
        if (!setting.config.wxqykf.url || !setting.config.wxqykf.corpId) {
          this.$toast("客服链接和企业ID不能为空");
          return;
        }
        common_vendor.wx$1.openCustomerServiceChat({
          extInfo: { url: setting.config.wxqykf.url },
          corpId: setting.config.wxqykf.corpId,
          showMessageCard: app.showCard,
          sendMessagePath: app.cardPath,
          sendMessageTitle: app.cardTitle,
          sendMessageImg: app.cardImage,
          success(res) {
          },
          fail(res) {
            console.log("wx.openCustomerServiceChat:fail", res);
          }
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isShow
  }, $data.isShow ? {
    b: $data.setting.provider == "mpwxkf" ? "contact" : "",
    c: $props.showCard,
    d: $props.cardPath,
    e: $props.cardTitle,
    f: $props.cardImage,
    g: common_vendor.o(($event) => $options.handleContact())
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
