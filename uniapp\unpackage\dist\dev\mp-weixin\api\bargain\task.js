"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "bargain.task/list",
  partake: "bargain.task/partake",
  detail: "bargain.task/detail",
  helpList: "bargain.task/helpList",
  helpCut: "bargain.task/helpCut"
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
const partake = (data) => {
  return utils_request_index.$http.post(api.partake, data);
};
const detail = (taskId, param) => {
  return utils_request_index.$http.get(api.detail, { taskId, ...param });
};
const helpList = (taskId, param) => {
  return utils_request_index.$http.get(api.helpList, { taskId, ...param });
};
const helpCut = (taskId, data) => {
  return utils_request_index.$http.post(api.helpCut, { taskId, ...data });
};
exports.detail = detail;
exports.helpCut = helpCut;
exports.helpList = helpList;
exports.list = list;
exports.partake = partake;
