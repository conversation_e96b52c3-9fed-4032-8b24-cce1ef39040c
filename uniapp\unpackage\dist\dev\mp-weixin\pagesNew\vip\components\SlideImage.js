"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    // 主图视频
    video: {
      type: Object,
      default() {
        return null;
      }
    },
    // 主图视频封面
    videoCover: {
      type: Object,
      default() {
        return null;
      }
    },
    // 图片轮播
    images: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      indicatorDots: true,
      // 是否显示面板指示点
      autoplay: true,
      // 是否自动切换
      interval: 4e3,
      // 自动切换时间间隔
      duration: 800,
      // 滑动动画时长
      currentIndex: 1
      // 轮播图指针
    };
  },
  methods: {
    // 事件：视频开始播放
    onVideoPlay(e) {
      this.autoplay = false;
    },
    // 设置轮播图当前指针 数字
    setCurrent({ detail }) {
      const app = this;
      app.currentIndex = detail.current + 1;
    },
    // 浏览商品图片
    onPreviewImages(index) {
      const app = this;
      const imageUrls = [];
      app.images.forEach((item) => {
        imageUrls.push(item.preview_url);
      });
      common_vendor.index.previewImage({
        current: imageUrls[index],
        urls: imageUrls
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.video
  }, $props.video ? {
    b: $props.videoCover ? $props.videoCover.preview_url : "",
    c: $props.video.external_url,
    d: common_vendor.o((...args) => $options.onVideoPlay && $options.onVideoPlay(...args))
  } : {}, {
    e: common_vendor.f($props.images, (item, index, i0) => {
      return {
        a: item.preview_url,
        b: index,
        c: common_vendor.o(($event) => $options.onPreviewImages(index), index)
      };
    }),
    f: $data.autoplay,
    g: $data.duration,
    h: $data.indicatorDots,
    i: $data.interval,
    j: common_vendor.o((...args) => $options.setCurrent && $options.setCurrent(...args)),
    k: common_vendor.t($data.currentIndex),
    l: common_vendor.t($props.images.length + ($props.video ? 1 : 0))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ae0e5004"]]);
wx.createComponent(Component);
