"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_model_Setting = require("../../../../common/model/Setting.js");
const CustomerBtn = () => "../../../customer-btn/index.js";
const _sfc_main = {
  components: {
    CustomerBtn
  },
  props: {
    itemStyle: Object,
    params: Object
  },
  data() {
    return {
      isShow: false
    };
  },
  // computed: {
  //   right() {
  //     return rpx2px(2 * this.itemStyle.right)
  //   },
  //   bottom() {
  //     return rpx2px(2 * this.itemStyle.bottom)
  //   }
  // },
  async created() {
    if (this.params.type === "phone") {
      this.isShow = true;
    }
    if (this.params.type === "chat") {
      this.isShow = await common_model_Setting.SettingModel.isShowCustomerBtn();
    }
  },
  methods: {
    /**
     * 点击拨打电话
     */
    onMakePhoneCall(e) {
      common_vendor.index.makePhoneCall({
        phoneNumber: this.params.tel
      });
    }
  }
};
if (!Array) {
  const _component_customer_btn = common_vendor.resolveComponent("customer-btn");
  _component_customer_btn();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isShow
  }, $data.isShow ? common_vendor.e({
    b: $props.params.type === "phone"
  }, $props.params.type === "phone" ? {
    c: $props.params.image,
    d: common_vendor.o((...args) => $options.onMakePhoneCall && $options.onMakePhoneCall(...args))
  } : $props.params.type === "chat" ? {
    f: $props.params.image
  } : {}, {
    e: $props.params.type === "chat",
    g: `${$props.itemStyle.right * 2}rpx`,
    h: `${$props.itemStyle.bottom * 2}rpx`
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-177bba1b"]]);
wx.createComponent(Component);
