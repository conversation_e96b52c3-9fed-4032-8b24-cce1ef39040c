<?php

declare (strict_types = 1);

namespace app\api\controller\xj;

use app\api\controller\Controller;
use app\api\model\xj\Exchange as ExchangeModel;
use think\response\Json;

/**
 * 优惠券兑换控制器
 * Class Exchange
 * @package app\api\controller\xj
 */
class Exchange extends Controller
{
    /**
     * 获取兑换页面数据
     * @return Json
     */
    public function index(): Json
    {
        // 临时简化版本用于测试
        try {
            $model = new ExchangeModel;
            $data = $model->getExchangeData();
            return $this->renderSuccess($data);
        } catch (\Exception $e) {
            return $this->renderError('接口错误: ' . $e->getMessage());
        }
    }

    /**
     * 兑换商品
     * @return Json
     */
    public function exchange(): Json
    {
        $model = new ExchangeModel;
        $data = [
            'product_id' => (int)$this->request->post('product_id'),
            'name' => $this->request->post('name'),
            'phone' => $this->request->post('phone'),
            'address' => $this->request->post('address')
        ];

        if (!$model->exchangeProduct($data)) {
            return $this->renderError($model->getError() ?: '兑换失败');
        }
        return $this->renderSuccess('兑换成功！');
    }
}
