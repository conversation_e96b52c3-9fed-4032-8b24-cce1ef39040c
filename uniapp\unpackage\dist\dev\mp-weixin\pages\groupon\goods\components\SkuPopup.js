"use strict";
const utils_color = require("../../../../utils/color.js");
require("../../../../utils/request/index.js");
const common_vendor = require("../../../../common/vendor.js");
const GoodsSkuPopup = () => "./goods-sku-popup/index.js";
const _sfc_main = {
  components: {
    GoodsSkuPopup
  },
  emits: ["update:modelValue"],
  props: {
    // 控制组件显示隐藏
    modelValue: {
      Type: Boolean,
      default: false
    },
    // 模式 1:都显示 2:只显示购物车 3:只显示立即购买
    skuMode: {
      type: Number,
      default: 1
    },
    // 商品详情信息
    goods: {
      type: Object,
      default: {}
    },
    // 购买模式 1:拼团购买 2:单独购买
    buyMode: {
      type: Number,
      default: 1
    },
    // 拼单ID (仅参与拼单时传入)
    taskId: {
      type: Number,
      default: void 0
    },
    // 阶梯团人数 (仅参与拼单时传入)
    stepPeople: {
      type: Number,
      default: void 0
    }
  },
  computed: {
    // 规格按钮选中时的背景色
    activedBtnBackgroundColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.1);
    }
  },
  watch: {
    buyMode(val) {
      this.init();
    }
  },
  data() {
    return {
      // 商品信息
      goodsInfo: {},
      // 限购数量
      maxBuyNum: null
    };
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化SKU数据
    init() {
      const app = this;
      const { goods } = app;
      app.goodsInfo = {
        _id: goods.goods_id,
        name: goods.goods_name,
        goods_thumb: goods.goods_image,
        sku_list: app.getSkuList(),
        spec_list: app.getSpecList(),
        active_type: goods.active_type,
        steps_config: goods.steps_config
      };
      app.maxBuyNum = app.getMaxBuyNum();
    },
    // 监听组件显示隐藏
    onChangeValue(val) {
      this.$emit("update:modelValue", val);
    },
    // 整理商品SKU列表
    getSkuList() {
      const app = this;
      const { goods: { goods_name, goods_image, skuList } } = app;
      const skuData = [];
      skuList.forEach((item) => {
        skuData.push({
          _id: item.id,
          goods_sku_id: item.goods_sku_id,
          goods_id: item.goods_id,
          goods_name,
          image: item.image_url ? item.image_url : goods_image,
          price: item.groupon_price,
          stock: item.stock_num,
          spec_value_ids: item.spec_value_ids,
          sku_name_arr: app.getSkuNameArr(item.spec_value_ids),
          groupon_price: item.groupon_price,
          original_price: item.original_price,
          steps_price_config: item.steps_price_config
        });
      });
      return skuData;
    },
    // 获取sku记录的规格值列表
    getSkuNameArr(specValueIds) {
      const app = this;
      const defaultData = ["默认"];
      const skuNameArr = [];
      if (specValueIds) {
        specValueIds.forEach((valueId, groupIndex) => {
          const specValueName = app.getSpecValueName(valueId, groupIndex);
          skuNameArr.push(specValueName);
        });
      }
      return skuNameArr.length ? skuNameArr : defaultData;
    },
    // 获取指定的规格值名称
    getSpecValueName(valueId, groupIndex) {
      const app = this;
      const { goods: { specList } } = app;
      const res = specList[groupIndex].valueList.find((specValue) => {
        return specValue.spec_value_id == valueId;
      });
      return res.spec_value;
    },
    // 整理规格数据
    getSpecList() {
      const { goods: { specList, steps_config } } = this;
      const defaultData = [{ name: "默认", list: [{ name: "默认" }] }];
      const specData = [];
      specList.forEach((group) => {
        const children = [];
        group.valueList.forEach((specValue) => {
          children.push({ name: specValue.spec_value });
        });
        specData.push({
          name: group.spec_name,
          list: children
        });
      });
      return specData.length ? specData : defaultData;
    },
    // 限购数量
    getMaxBuyNum() {
      const { goods, buyMode } = this;
      return buyMode == 1 && goods.is_restrict ? goods.restrict_single : null;
    },
    // sku组件 开始-----------------------------------------------------------
    openSkuPopup() {
    },
    closeSkuPopup() {
    },
    // 立即购买
    buyNow(selectShop) {
      const app = this;
      app.$navTo("pages/checkout/index", app.getOrderParam(selectShop));
      app.onChangeValue(false);
    },
    // 生成下单参数
    getOrderParam(selectShop) {
      const { goods, buyMode, taskId } = this;
      const param = {
        goodsSkuId: selectShop.goods_sku_id,
        goodsNum: selectShop.buy_num
      };
      if (buyMode == 1) {
        param.mode = "groupon";
        param.grouponGoodsId = goods.groupon_goods_id;
        param.taskId = taskId;
        param.stepPeople = selectShop.stepPeople;
      } else {
        param.mode = "buyNow";
        param.goodsId = goods.goods_id;
      }
      return param;
    }
  }
};
if (!Array) {
  const _component_goods_sku_popup = common_vendor.resolveComponent("goods-sku-popup");
  _component_goods_sku_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeValue),
    b: common_vendor.o($options.openSkuPopup),
    c: common_vendor.o($options.closeSkuPopup),
    d: common_vendor.o($options.buyNow),
    e: common_vendor.p({
      modelValue: $props.modelValue,
      ["border-radius"]: "20",
      localdata: $data.goodsInfo,
      mode: $props.skuMode,
      maskCloseAble: true,
      priceColor: _ctx.appTheme.mainBg,
      buyNowBackgroundColor: _ctx.appTheme.mainBg,
      addCartColor: _ctx.appTheme.viceText,
      addCartBackgroundColor: _ctx.appTheme.viceBg,
      activedStyle: {
        color: _ctx.appTheme.mainBg,
        borderColor: _ctx.appTheme.mainBg,
        backgroundColor: $options.activedBtnBackgroundColor
      },
      buyNowText: "立即购买",
      maxBuyNum: $data.maxBuyNum,
      buyMode: $props.buyMode,
      isJoin: !!$props.taskId,
      stepPeople: $props.stepPeople
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
