"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_util = require("../../utils/util.js");
const common_enum_order_DeliveryStatus = require("../../common/enum/order/DeliveryStatus.js");
const common_enum_order_DeliveryType = require("../../common/enum/order/DeliveryType.js");
require("../../common/enum/order/OrderSource.js");
const common_enum_order_OrderStatus = require("../../common/enum/order/OrderStatus.js");
const common_enum_order_PayStatus = require("../../common/enum/order/PayStatus.js");
const common_enum_order_ReceiptStatus = require("../../common/enum/order/ReceiptStatus.js");
const common_enum_order_OrderType = require("../../common/enum/order/OrderType.js");
const common_enum_Client = require("../../common/enum/Client.js");
const common_enum_payment_Method = require("../../common/enum/payment/Method.js");
const common_enum_order_delivery_DeliveryMethod = require("../../common/enum/order/delivery/DeliveryMethod.js");
const api_order = require("../../api/order.js");
const common_assets = require("../../common/assets.js");
let listener = false;
const _sfc_main = {
  data() {
    return {
      // 外部方法
      inArray: utils_util.inArray,
      // 枚举类
      OrderTypeEnum: common_enum_order_OrderType.OrderTypeEnum,
      DeliveryStatusEnum: common_enum_order_DeliveryStatus.DeliveryStatusEnum,
      DeliveryTypeEnum: common_enum_order_DeliveryType.DeliveryTypeEnum,
      OrderStatusEnum: common_enum_order_OrderStatus.OrderStatusEnum,
      PayStatusEnum: common_enum_order_PayStatus.PayStatusEnum,
      ReceiptStatusEnum: common_enum_order_ReceiptStatus.ReceiptStatusEnum,
      DeliveryMethodEnum: common_enum_order_delivery_DeliveryMethod.DeliveryMethodEnum,
      PayMethodEnum: common_enum_payment_Method.PayMethodEnum,
      // 当前订单ID
      orderId: null,
      // 加载中
      isLoading: true,
      // 当前订单详情
      order: {},
      // 当前设置
      setting: {},
      // 核销二维码弹窗
      showQRCodePopup: false,
      // 核销二维码图片url (通过后端获取)
      qrcodeImage: "",
      // 控制onShow事件是否刷新订单信息
      canReset: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ orderId }) {
    this.orderId = orderId;
    this.getOrderDetail();
    common_vendor.index.$on("syncRefresh", (val, isCur) => {
      if (!isCur) {
        this.canReset = val;
      }
    });
    this.listenerBusinessView();
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    common_vendor.wx$1.offAppShow(listener);
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.canReset && this.getOrderDetail();
    this.canReset = false;
  },
  methods: {
    // 获取当前订单信息
    getOrderDetail(canReset = false) {
      const app = this;
      app.isLoading = true;
      api_order.detail(app.orderId).then((result) => {
        app.order = result.data.order;
        app.setting = result.data.setting;
        app.isLoading = false;
      });
      canReset && common_vendor.index.$emit("syncRefresh", true, true);
    },
    // 复制指定内容
    handleCopy(value) {
      const app = this;
      common_vendor.index.setClipboardData({
        data: value,
        success: () => app.$toast("复制成功"),
        fail: ({ errMsg }) => app.$toast("复制失败 " + errMsg)
      });
    },
    // 跳转到门店详情页
    handleTargetExtract(shopId) {
      this.$navTo("pages/shop/detail", { shopId });
    },
    // 跳转到物流跟踪页面
    handleTargetExpress() {
      this.$navTo("pages/order/express/index", { orderId: this.orderId });
    },
    // 跳转到商品详情页面
    handleTargetGoods(goodsId) {
      if (this.order.type == 0) {
        this.$navTo("pages/goods/detail", {
          goodsId
        });
      }
      if (this.order.type == 1) {
        this.$navTo("pagesNew/vip/detail", {
          goodsId
        });
      }
      if (this.order.type == 2) {
        this.$navTo("pagesNew/points/detail", {
          goodsId
        });
      }
    },
    // 跳转到申请售后页面
    handleApplyRefund(orderGoodsId) {
      var type = this.order.type;
      this.$navTo("pages/refund/apply", { orderGoodsId, type });
    },
    // 取消订单
    onCancel(orderId) {
      const app = this;
      common_vendor.index.showModal({
        title: "友情提示",
        content: "确认要取消该订单吗？",
        success(o) {
          if (o.confirm) {
            api_order.cancel(orderId).then((result) => {
              app.$toast(result.message);
              setTimeout(() => app.getOrderDetail(true), 1500);
            });
          }
        }
      });
    },
    // 确认收货
    async onReceipt(orderId) {
      const app = this;
      if (app.platform === common_enum_Client.ClientEnum.MP_WEIXIN.value && app.order.sync_weixin_shipping && app.order.pay_method === common_enum_payment_Method.PayMethodEnum.WECHAT.value && app.order.platform === common_enum_Client.ClientEnum.MP_WEIXIN.value) {
        app.openBusinessView();
      } else {
        app.receiptModal(orderId);
      }
    },
    // 确认收货弹窗 (微信小程序提供的用于同步发货信息管理)
    openBusinessView() {
      const app = this;
      return new Promise((resolve, reject) => {
        if (!common_vendor.wx$1.openBusinessView) {
          console.log("不支持 wx.openBusinessView");
          resolve(false);
        }
        common_vendor.wx$1.openBusinessView({
          businessType: "weappOrderConfirm",
          extraData: { transaction_id: app.order.trade.trade_no },
          success() {
            console.log("拉起确认收货组件 success");
            resolve(true);
          },
          fail(err) {
            console.error("拉起确认收货组件 fail", err);
            resolve(false);
          }
        });
      });
    },
    // 微信小程序确认收货组件 - 回调监听
    listenerBusinessView() {
      if (listener !== false) {
        common_vendor.wx$1.offAppShow(listener);
      }
      listener = common_vendor.wx$1.onAppShow(({ referrerInfo }) => {
        console.log("wx.onAppShow", this.orderId, referrerInfo.extraData);
        if (referrerInfo.extraData && referrerInfo.extraData.status && referrerInfo.extraData.status === "success") {
          console.log("success receiptEvent", this.orderId);
          this.receiptEvent(this.orderId);
        }
      });
    },
    // 确认收货弹窗 (系统默认)
    receiptModal(orderId) {
      const app = this;
      common_vendor.index.showModal({
        title: "友情提示",
        content: "确认收到商品了吗？",
        success(o) {
          o.confirm && app.receiptEvent(orderId);
        }
      });
    },
    // 确认收货事件
    receiptEvent(orderId) {
      const app = this;
      api_order.receipt(orderId).then((result) => {
        app.$success(result.message);
        setTimeout(() => app.getOrderDetail(true), 1500);
      });
    },
    // 获取核销二维码
    onExtractQRCode(orderId) {
      const app = this;
      api_order.extractQrcode(orderId, { channel: app.platform }).then((result) => {
        app.qrcodeImage = result.data.qrcode;
        app.showQRCodePopup = true;
      });
    },
    // 点击去支付
    onPay(orderId) {
      this.$navTo("pages/checkout/cashier/index", { orderId });
    },
    // 跳转到订单评价页
    handleTargetComment(orderId) {
      this.$navTo("pages/order/comment/index", { orderId });
    }
  }
};
if (!Array) {
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  _easycom_u_popup2();
}
const _easycom_u_popup = () => "../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
if (!Math) {
  _easycom_u_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.order.order_status == $data.OrderStatusEnum.NORMAL.value
  }, $data.order.order_status == $data.OrderStatusEnum.NORMAL.value ? common_vendor.e({
    c: $data.order.pay_status == $data.PayStatusEnum.PENDING.value
  }, $data.order.pay_status == $data.PayStatusEnum.PENDING.value ? {
    d: common_assets._imports_0$2
  } : $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
    f: common_assets._imports_1
  } : $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value ? {
    h: common_assets._imports_2
  } : {}, {
    e: $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value,
    g: $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value
  }) : {}, {
    i: $data.order.order_status == $data.OrderStatusEnum.COMPLETED.value
  }, $data.order.order_status == $data.OrderStatusEnum.COMPLETED.value ? {
    j: common_assets._imports_3
  } : {}, {
    k: $data.order.order_status == $data.OrderStatusEnum.CANCELLED.value || $data.order.order_status == $data.OrderStatusEnum.APPLY_CANCEL.value
  }, $data.order.order_status == $data.OrderStatusEnum.CANCELLED.value || $data.order.order_status == $data.OrderStatusEnum.APPLY_CANCEL.value ? {
    l: common_assets._imports_4
  } : {}, {
    m: common_vendor.t($data.order.state_text),
    n: $data.order.order_status == $data.OrderStatusEnum.NORMAL.value
  }, $data.order.order_status == $data.OrderStatusEnum.NORMAL.value ? common_vendor.e({
    o: $data.order.pay_status == $data.PayStatusEnum.PENDING.value
  }, $data.order.pay_status == $data.PayStatusEnum.PENDING.value ? {
    p: common_vendor.o(($event) => $options.onPay($data.order.order_id))
  } : {}, {
    q: $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value
  }, $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value ? {
    r: common_vendor.o(($event) => $options.onReceipt($data.order.order_id))
  } : {}) : {}, {
    s: $data.order.type == $data.OrderTypeEnum.PHYSICAL.value
  }, $data.order.type == $data.OrderTypeEnum.PHYSICAL.value ? common_vendor.e({
    t: $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value
  }, $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value ? {
    v: common_vendor.t($data.order.address.name),
    w: common_vendor.t($data.order.address.phone),
    x: common_vendor.f($data.order.address.region, (region, idx, i0) => {
      return {
        a: common_vendor.t(region),
        b: idx
      };
    }),
    y: common_vendor.t($data.order.address.detail)
  } : {}, {
    z: $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value
  }, $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value ? {
    A: common_vendor.t($data.order.extract_shop.shop_name),
    B: common_vendor.t($data.order.extract_shop.region.province),
    C: common_vendor.t($data.order.extract_shop.region.city),
    D: common_vendor.t($data.order.extract_shop.region.region),
    E: common_vendor.t($data.order.extract_shop.address),
    F: common_vendor.o(($event) => $options.handleTargetExtract($data.order.extract_shop.shop_id))
  } : {}) : {}, {
    G: $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value && $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.delivery && $data.order.delivery.length
  }, $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value && $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.delivery && $data.order.delivery.length ? common_vendor.e({
    H: $data.order.delivery.length > 1
  }, $data.order.delivery.length > 1 ? {
    I: common_vendor.t($data.order.delivery.length)
  } : common_vendor.e({
    J: $data.order.delivery[0].delivery_method != $data.DeliveryMethodEnum.UNWANTED.value
  }, $data.order.delivery[0].delivery_method != $data.DeliveryMethodEnum.UNWANTED.value ? {
    K: common_vendor.t($data.order.delivery[0].express ? $data.order.delivery[0].express.express_name : "--")
  } : {}, {
    L: common_vendor.t($data.order.delivery[0].express_no ? $data.order.delivery[0].express_no : "--"),
    M: $data.order.delivery[0].express_no
  }, $data.order.delivery[0].express_no ? {
    N: common_vendor.o(($event) => $options.handleCopy($data.order.delivery[0].express_no))
  } : {}), {
    O: common_vendor.o(($event) => $options.handleTargetExpress())
  }) : {}, {
    P: common_vendor.f($data.order.goods, (goods, idx, i0) => {
      return common_vendor.e({
        a: goods.goods_image,
        b: common_vendor.t(goods.goods_name),
        c: common_vendor.f(goods.goods_props, (props, idx2, i1) => {
          return {
            a: common_vendor.t(props.value.name),
            b: idx2
          };
        })
      }, $data.order.type == 0 ? {
        d: common_vendor.t(goods.is_user_grade ? goods.grade_goods_price : goods.goods_price)
      } : {}, $data.order.type == 1 ? {} : {}, $data.order.type == 2 ? {
        e: common_vendor.t(Number(goods.goods_price))
      } : {}, {
        f: common_vendor.t(goods.total_num),
        g: common_vendor.o(($event) => $options.handleTargetGoods(goods.goods_id), idx),
        h: goods.refund
      }, goods.refund ? {} : $data.order.type != 1 && $data.order.isAllowRefund && goods.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.receipt_status == $data.ReceiptStatusEnum.RECEIVED.value ? {
        j: common_vendor.o(($event) => $options.handleApplyRefund(goods.order_goods_id), idx)
      } : {}, {
        i: $data.order.type != 1 && $data.order.isAllowRefund && goods.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.receipt_status == $data.ReceiptStatusEnum.RECEIVED.value,
        k: idx
      });
    }),
    Q: $data.order.type == 0,
    R: $data.order.type == 1,
    S: $data.order.type == 2,
    T: common_vendor.t($data.order.order_no),
    U: common_vendor.o(($event) => $options.handleCopy($data.order.order_no)),
    V: common_vendor.t($data.order.create_time),
    W: common_vendor.t($data.order.buyer_remark ? $data.order.buyer_remark : "--"),
    X: $data.order.type != 2
  }, $data.order.type != 2 ? {
    Y: common_vendor.t($data.order.total_price)
  } : {
    Z: common_vendor.t(Number($data.order.points_money))
  }, {
    aa: $data.order.coupon_money > 0
  }, $data.order.coupon_money > 0 ? {
    ab: common_vendor.t($data.order.coupon_money)
  } : {}, {
    ac: $data.order.type != 2
  }, $data.order.type != 2 ? {
    ad: common_vendor.t($data.order.express_price)
  } : {}, {
    ae: $data.order.update_price.value != "0.00"
  }, $data.order.update_price.value != "0.00" ? {
    af: common_vendor.t($data.order.update_price.symbol),
    ag: common_vendor.t($data.order.update_price.value)
  } : {}, {
    ah: $data.order.type != 2
  }, $data.order.type != 2 ? {
    ai: common_vendor.t($data.order.pay_price)
  } : {}, {
    aj: $data.order.order_status != $data.OrderStatusEnum.CANCELLED.value
  }, $data.order.order_status != $data.OrderStatusEnum.CANCELLED.value ? common_vendor.e({
    ak: $data.order.pay_status == $data.PayStatusEnum.PENDING.value
  }, $data.order.pay_status == $data.PayStatusEnum.PENDING.value ? {
    al: common_vendor.o(($event) => $options.onCancel($data.order.order_id))
  } : {}, {
    am: $data.order.order_status != $data.OrderStatusEnum.APPLY_CANCEL.value
  }, $data.order.order_status != $data.OrderStatusEnum.APPLY_CANCEL.value ? common_vendor.e({
    an: $data.order.type != 2 && $data.order.pay_status == $data.PayStatusEnum.SUCCESS.value && $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value
  }, $data.order.type != 2 && $data.order.pay_status == $data.PayStatusEnum.SUCCESS.value && $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
    ao: common_vendor.o(($event) => $options.onCancel($data.order.order_id))
  } : {}, {
    ap: $data.order.pay_status == $data.PayStatusEnum.SUCCESS.value && $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value && $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value
  }, $data.order.pay_status == $data.PayStatusEnum.SUCCESS.value && $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value && $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
    aq: common_vendor.o(($event) => $options.onExtractQRCode($data.order.order_id))
  } : {}) : {}, {
    ar: $data.order.pay_status == $data.PayStatusEnum.PENDING.value && $data.order.pay_method != $data.PayMethodEnum.OFFLINE.value
  }, $data.order.pay_status == $data.PayStatusEnum.PENDING.value && $data.order.pay_method != $data.PayMethodEnum.OFFLINE.value ? {
    as: common_vendor.o(($event) => $options.onPay($data.order.order_id))
  } : {}, {
    at: $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value
  }, $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value ? {
    av: common_vendor.o(($event) => $options.onReceipt($data.order.order_id))
  } : {}, {
    aw: $data.order.order_status == $data.OrderStatusEnum.COMPLETED.value && $data.order.is_comment == 0
  }, $data.order.order_status == $data.OrderStatusEnum.COMPLETED.value && $data.order.is_comment == 0 ? {
    ax: common_vendor.o(($event) => $options.handleTargetComment($data.order.order_id))
  } : {}) : {}, {
    ay: $data.qrcodeImage
  }, $data.qrcodeImage ? {
    az: $data.qrcodeImage
  } : {}, {
    aA: common_vendor.o(($event) => $data.showQRCodePopup = $event),
    aB: common_vendor.p({
      mode: "center",
      ["border-radius"]: "26",
      closeable: true,
      modelValue: $data.showQRCodePopup
    }),
    aC: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6b23c96c"]]);
wx.createPage(MiniProgramPage);
