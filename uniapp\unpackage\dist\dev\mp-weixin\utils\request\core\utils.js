"use strict";
const common_vendor = require("../../../common/vendor.js");
const mergeConfig = (_this, options) => {
  let urlType = /^(http|https):\/\//.test(options.url);
  let config = Object.assign({
    timeout: _this.timeout
  }, _this.config, options);
  if (options.method == "FILE") {
    config.url = urlType ? options.url : _this.fileUrl + options.url;
  } else {
    config.url = urlType ? options.url : _this.baseUrl + options.url;
  }
  if (options.header) {
    config.header = Object.assign({}, _this.header, options.header);
  } else {
    config.header = Object.assign({}, _this.header);
  }
  return config;
};
const dispatchRequest = (requestInfo) => {
  return new Promise((resolve, reject) => {
    let requestAbort = true;
    let requestData = {
      url: requestInfo.url,
      header: requestInfo.header,
      //加入请求头
      success: (res) => {
        requestAbort = false;
        resolve(res);
      },
      fail: (err) => {
        requestAbort = false;
        if (err.errMsg == "request:fail abort") {
          reject({
            errMsg: "请求超时，请重新尝试",
            statusCode: 0
          });
        } else {
          reject(err);
        }
      }
    };
    if (requestInfo.method) {
      requestData.method = requestInfo.method;
    }
    if (requestInfo.data) {
      requestData.data = requestInfo.data;
    }
    if (requestInfo.timeout) {
      requestData.timeout = requestInfo.timeout;
    }
    if (requestInfo.dataType) {
      requestData.dataType = requestInfo.dataType;
    }
    if (requestInfo.responseType) {
      requestData.responseType = requestInfo.responseType;
    }
    let requestTask = common_vendor.index.request(requestData);
    setTimeout(() => {
      if (requestAbort) {
        requestTask.abort();
      }
    }, requestInfo.timeout);
  });
};
const jsonpRequest = (requestInfo) => {
  return new Promise((resolve, reject) => {
    let dataStr = "";
    Object.keys(requestInfo.data).forEach((key) => {
      dataStr += key + "=" + requestInfo.data[key] + "&";
    });
    if (dataStr !== "") {
      dataStr = dataStr.substr(0, dataStr.lastIndexOf("&"));
    }
    requestInfo.url = requestInfo.url + "?" + dataStr;
  });
};
exports.dispatchRequest = dispatchRequest;
exports.jsonpRequest = jsonpRequest;
exports.mergeConfig = mergeConfig;
