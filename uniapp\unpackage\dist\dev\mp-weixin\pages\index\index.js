"use strict";
const common_vendor = require("../../common/vendor.js");
const core_app = require("../../core/app.js");
const api_page = require("../../api/page.js");
const core_platform = require("../../core/platform.js");
const Page = () => "../../components/page/index.js";
const PrivacyPopup = () => "../../components/privacy-popup/index.js";
const PromotePopup = () => "../../components/promote-popup/index.js";
getApp();
const _sfc_main = {
  components: {
    Page,
    PrivacyPopup,
    PromotePopup
  },
  data() {
    return {
      // 页面参数
      options: {},
      // 页面属性
      page: {},
      // 页面元素
      items: [],
      // 启用开屏推广
      enablePromote: core_platform.platfrom != "MP-WEIXIN"
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    this.getPageData();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    core_app.setCartTabBadge();
  },
  methods: {
    /**
     * 加载页面数据
     * @param {Object} callback
     */
    getPageData(callback) {
      const app = this;
      const pageId = app.options.pageId || 0;
      api_page.detail(pageId).then((result) => {
        const { data: { pageData } } = result;
        app.page = pageData.page;
        app.items = pageData.items;
        app.setPageBar();
      }).finally(() => callback && callback());
    },
    /**
     * 设置顶部导航栏
     */
    setPageBar() {
      const { page } = this;
      common_vendor.index.setNavigationBarTitle({
        title: page.params.title
      });
      common_vendor.index.setNavigationBarColor({
        frontColor: page.style.titleTextColor === "white" ? "#ffffff" : "#000000",
        backgroundColor: page.style.titleBackgroundColor
      });
    },
    // 用户隐私保护提示结束事件
    onPrivacyEnd() {
      this.enablePromote = true;
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.getPageData(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const { page } = app;
    return {
      title: page.params.shareTitle,
      path: "/pages/index/index?" + app.$getShareUrlParams()
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const { page } = app;
    return {
      title: page.params.shareTitle,
      path: "/pages/index/index?" + app.$getShareUrlParams()
    };
  }
};
if (!Array) {
  const _component_Page = common_vendor.resolveComponent("Page");
  const _component_PromotePopup = common_vendor.resolveComponent("PromotePopup");
  const _component_PrivacyPopup = common_vendor.resolveComponent("PrivacyPopup");
  (_component_Page + _component_PromotePopup + _component_PrivacyPopup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      items: $data.items
    }),
    b: $data.enablePromote
  }, $data.enablePromote ? {} : {}, {
    c: common_vendor.o(($event) => $options.onPrivacyEnd()),
    d: common_vendor.p({
      hideTabBar: true
    }),
    e: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
