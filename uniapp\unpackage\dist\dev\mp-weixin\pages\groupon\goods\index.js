"use strict";
const core_mixins_wxofficial = require("../../../core/mixins/wxofficial.js");
const core_app = require("../../../core/app.js");
const api_groupon_goods = require("../../../api/groupon/goods.js");
const api_cart = require("../../../api/cart.js");
const common_model_Setting = require("../../../common/model/Setting.js");
require("../../../common/enum/goods/GoodsType.js");
require("../../../common/enum/goods/SpecType.js");
const common_enum_goods_GoodsSource = require("../../../common/enum/goods/GoodsSource.js");
require("../../../common/enum/groupon/GoodsStatus.js");
require("../../../common/enum/groupon/TaskStatus.js");
const common_enum_groupon_ActiveType = require("../../../common/enum/groupon/ActiveType.js");
const common_enum_groupon_ActiveStatus = require("../../../common/enum/groupon/ActiveStatus.js");
const common_vendor = require("../../../common/vendor.js");
const ShareSheet = () => "../../../components/share-sheet/index.js";
const CustomerBtn = () => "../../../components/customer-btn/index.js";
const SkuPopup = () => "./components/SkuPopup.js";
const TaskList = () => "./components/TaskList.js";
const SlideImage = () => "../../goods/components/SlideImage.js";
const Comment = () => "../../goods/components/Comment.js";
const Market = () => "../../goods/components/Market.js";
const CountDown = () => "../../../components/countdown/index.js";
const _sfc_main = {
  components: {
    ShareSheet,
    CustomerBtn,
    SlideImage,
    TaskList,
    SkuPopup,
    Comment,
    Market,
    CountDown
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 枚举类
      ActiveTypeEnum: common_enum_groupon_ActiveType.ActiveTypeEnum,
      ActiveStatusEnum: common_enum_groupon_ActiveStatus.ActiveStatusEnum,
      GoodsSourceEnum: common_enum_goods_GoodsSource.GoodsSourceEnum,
      // 显示/隐藏SKU弹窗
      showSkuPopup: false,
      // 按钮模式 1:都显示 2:只显示购物车 3:只显示立即购买
      skuMode: 3,
      // 购买模式 1:拼团购买 2:单独购买
      buyMode: 1,
      // 显示/隐藏分享菜单
      showShareSheet: false,
      // 获取商品海报图api方法
      posterApiCall: api_groupon_goods.poster,
      // 显示拼团规则
      showRules: false,
      // 拼团规则内容
      setting: {},
      // 当前拼团商品ID
      grouponGoodsId: null,
      // 拼团商品详情
      goods: {},
      // 购物车总数量
      cartTotal: 0,
      // 是否显示在线客服按钮
      isShowCustomerBtn: false
    };
  },
  computed: {
    // 当前页面链接
    pagePath() {
      const params = this.$getShareUrlParams({ grouponGoodsId: this.grouponGoodsId });
      return `/pages/groupon/goods/detail?${params}`;
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.onRecordQuery(options);
    this.onRefreshPage();
    this.isShowCustomerBtn = await common_model_Setting.SettingModel.isShowCustomerBtn();
  },
  methods: {
    // 记录query参数
    onRecordQuery(query) {
      const scene = core_app.getSceneData(query);
      this.grouponGoodsId = query.grouponGoodsId ? parseInt(query.grouponGoodsId) : parseInt(scene.gid);
    },
    // 刷新页面数据
    onRefreshPage() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getActiveDetail(), app.getCartTotal()]).then(() => app.setWxofficialShareData()).finally(() => app.isLoading = false);
    },
    // 获取拼团活动详情
    getActiveDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_groupon_goods.detail(app.grouponGoodsId).then((result) => {
          app.goods = result.data.detail;
          app.setting = result.data.setting;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取购物车总数量
    getCartTotal() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_cart.total().then((result) => {
          app.cartTotal = result.data.cartTotal;
          resolve(result);
        }).catch(reject);
      });
    },
    // 显示拼团规则
    handleShowRules() {
      this.showRules = true;
    },
    // 显示/隐藏SKU弹窗
    onShowSkuPopup(buyMode = 1) {
      this.buyMode = buyMode;
      this.showSkuPopup = !this.showSkuPopup;
    },
    // 显示隐藏分享菜单
    onShowShareSheet() {
      this.showShareSheet = !this.showShareSheet;
    },
    // 跳转到首页
    onTargetHome(e) {
      this.$navTo("pages/index/index");
    },
    // 跳转到购物车页
    onTargetCart() {
      this.$navTo("pages/cart/index");
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const { goods } = this;
      this.updateShareCardData({
        title: goods.goods_name,
        desc: goods.selling_point,
        imgUrl: goods.goods_image
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  }
};
if (!Array) {
  const _component_SlideImage = common_vendor.resolveComponent("SlideImage");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _component_Market = common_vendor.resolveComponent("Market");
  const _component_TaskList = common_vendor.resolveComponent("TaskList");
  const _component_SkuPopup = common_vendor.resolveComponent("SkuPopup");
  const _component_Comment = common_vendor.resolveComponent("Comment");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _component_customer_btn = common_vendor.resolveComponent("customer-btn");
  const _component_share_sheet = common_vendor.resolveComponent("share-sheet");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_component_SlideImage + _component_count_down + _component_Market + _component_TaskList + _component_SkuPopup + _component_Comment + _easycom_mp_html2 + _component_customer_btn + _component_share_sheet + _easycom_u_modal2)();
}
const _easycom_mp_html = () => "../../../uni_modules/mp-html/components/mp-html/mp-html.js";
const _easycom_u_modal = () => "../../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_mp_html + _easycom_u_modal)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: common_vendor.p({
      video: $data.goods.video,
      videoCover: $data.goods.videoCover,
      images: $data.goods.goods_images
    })
  } : {}, {
    c: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    d: common_vendor.t($data.goods.active_type != $data.ActiveTypeEnum.NORMAL.value ? $data.ActiveTypeEnum[$data.goods.active_type].name2 : "多人拼团"),
    e: common_vendor.t($data.goods.groupon_price),
    f: common_vendor.t($data.goods.original_price),
    g: common_vendor.t($data.goods.active_sales),
    h: common_vendor.t($data.goods.goods_name),
    i: common_vendor.o(($event) => $options.onShowShareSheet()),
    j: $data.goods.selling_point
  }, $data.goods.selling_point ? {
    k: common_vendor.t($data.goods.selling_point)
  } : {}, {
    l: $data.goods.active_status != $data.ActiveStatusEnum.STATE_END.value
  }, $data.goods.active_status != $data.ActiveStatusEnum.STATE_END.value ? {
    m: common_vendor.t($data.goods.active_status == $data.ActiveStatusEnum.STATE_SOON.value ? "开始" : "结束"),
    n: common_vendor.p({
      date: $data.goods.end_time,
      separator: "zh",
      theme: "text"
    })
  } : {}, {
    o: !$data.isLoading
  }, !$data.isLoading ? {
    p: common_vendor.p({
      ["goods-source"]: $data.GoodsSourceEnum.GROUPON.value
    })
  } : {}) : {}, {
    q: !$data.isLoading
  }, !$data.isLoading ? {
    r: common_vendor.p({
      grouponGoodsId: $data.goods.groupon_goods_id,
      list: $data.goods.taskQuickJoinList
    })
  } : {}, {
    s: $data.goods.spec_type == 20
  }, $data.goods.spec_type == 20 ? {
    t: common_vendor.f($data.goods.specList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.spec_name),
        b: index
      };
    }),
    v: common_vendor.o(($event) => $options.onShowSkuPopup(1))
  } : {}, {
    w: common_vendor.o(($event) => $options.handleShowRules()),
    x: !$data.isLoading
  }, !$data.isLoading ? {
    y: common_vendor.o(($event) => $data.showSkuPopup = $event),
    z: common_vendor.p({
      skuMode: $data.skuMode,
      goods: $data.goods,
      buyMode: $data.buyMode,
      modelValue: $data.showSkuPopup
    })
  } : {}, {
    A: !$data.isLoading
  }, !$data.isLoading ? {
    B: common_vendor.p({
      ["goods-id"]: $data.goods.goods_id,
      limit: 2
    })
  } : {}, {
    C: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    D: $data.goods.content != ""
  }, $data.goods.content != "" ? {
    E: common_vendor.p({
      content: $data.goods.content
    })
  } : {}) : {}, {
    F: common_vendor.o((...args) => $options.onTargetHome && $options.onTargetHome(...args)),
    G: $data.isShowCustomerBtn
  }, $data.isShowCustomerBtn ? {
    H: common_vendor.p({
      showCard: true,
      cardTitle: $data.goods.goods_name,
      cardImage: $data.goods.goods_image,
      cardPath: $options.pagePath
    })
  } : {}, {
    I: !$data.isShowCustomerBtn
  }, !$data.isShowCustomerBtn ? common_vendor.e({
    J: $data.cartTotal > 0
  }, $data.cartTotal > 0 ? {
    K: common_vendor.t($data.cartTotal > 99 ? "99+" : $data.cartTotal)
  } : {}, {
    L: common_vendor.o((...args) => $options.onTargetCart && $options.onTargetCart(...args))
  }) : {}, {
    M: $data.goods.active_status == $data.ActiveStatusEnum.STATE_BEGIN.value
  }, $data.goods.active_status == $data.ActiveStatusEnum.STATE_BEGIN.value ? common_vendor.e({
    N: $data.goods.is_alone_buy
  }, $data.goods.is_alone_buy ? {
    O: common_vendor.t($data.goods.original_price),
    P: common_vendor.o(($event) => $options.onShowSkuPopup(2))
  } : {}, {
    Q: common_vendor.t($data.goods.groupon_price),
    R: common_vendor.o(($event) => $options.onShowSkuPopup(1))
  }) : {
    S: common_vendor.t($data.goods.active_status == $data.ActiveStatusEnum.STATE_SOON.value ? "活动未开始" : "活动已结束")
  }, {
    T: common_vendor.o(($event) => $data.showShareSheet = $event),
    U: common_vendor.p({
      shareTitle: $data.goods.goods_name,
      shareImageUrl: $data.goods.goods_image,
      posterApiCall: $data.posterApiCall,
      posterApiParam: {
        grouponGoodsId: $data.grouponGoodsId
      },
      modelValue: $data.showShareSheet
    }),
    V: !$data.isLoading
  }, !$data.isLoading ? {
    W: common_vendor.t($data.setting.ruleDetail),
    X: common_vendor.o(($event) => $data.showRules = $event),
    Y: common_vendor.p({
      title: "拼团规则",
      modelValue: $data.showRules
    })
  } : {}, {
    Z: !$data.isLoading,
    aa: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7a68e073"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
