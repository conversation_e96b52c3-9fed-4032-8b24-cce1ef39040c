"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "comment/list",
  listRows: "comment/listRows",
  total: "comment/total"
};
const list = (goodsId, param, option) => {
  return utils_request_index.$http.get(api.list, { ...param, goodsId }, option);
};
const listRows = (goodsId, limit = 5) => {
  return utils_request_index.$http.get(api.listRows, { goodsId, limit });
};
const total = (goodsId) => {
  return utils_request_index.$http.get(api.total, { goodsId });
};
exports.list = list;
exports.listRows = listRows;
exports.total = total;
