"use strict";
const common_vendor = require("../../common/vendor.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
const api_comment = require("../../api/comment.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const pageSize = 15;
const tabs = [{
  name: `全部`,
  scoreType: -1
}, {
  name: `好评`,
  scoreType: 10
}, {
  name: `中评`,
  scoreType: 20
}, {
  name: `差评`,
  scoreType: 30
}];
const _sfc_main = {
  components: {
    AvatarImage
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 当前商品ID
      goodsId: null,
      // 当前标签索引
      curTab: 0,
      // 评价列表数据
      list: core_app.getEmptyPaginateObj(),
      // 评价总数量
      total: { all: 0, negative: 0, praise: 0, review: 0 },
      // 评星数据转换
      rates: { 10: 5, 20: 3, 30: 1 },
      // 标签栏数据
      tabs,
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于4条才显示无更多数据
        noMoreSize: 4,
        // 空布局
        empty: {
          tip: "亲，暂无相关商品评价"
        }
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.goodsId = options.goodsId;
    this.getTotal();
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getCommentList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 加载评价列表数据
    getCommentList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_comment.list(app.goodsId, { scoreType: app.getScoreType(), page: pageNo }, { load: false }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    },
    // 评分类型
    getScoreType() {
      return this.tabs[this.curTab].scoreType;
    },
    // 获取指定评分总数
    getTotal() {
      const app = this;
      api_comment.total(app.goodsId).then((result) => {
        const total = result.data.total;
        app.getTabs(total);
      });
    },
    // 获取tab标签内容
    getTabs(total) {
      const tabs2 = this.tabs;
      tabs2[0].name = `全部(${total.all})`;
      tabs2[1].name = `好评(${total.praise})`;
      tabs2[2].name = `中评(${total.review})`;
      tabs2[3].name = `差评(${total.negative})`;
    },
    // 切换标签项
    onChangeTab(index) {
      const app = this;
      app.curTab = index;
      app.onRefreshList();
    },
    // 刷新评价列表
    onRefreshList() {
      this.list = core_app.getEmptyPaginateObj();
      setTimeout(() => {
        this.mescroll.resetUpScroll();
      }, 120);
    },
    // 预览评价图片
    onPreviewImages(dataIdx, imgIndex) {
      const app = this;
      const images = app.list.data[dataIdx].images;
      const imageUrls = images.map((item) => item.image_url);
      common_vendor.index.previewImage({
        current: imageUrls[imgIndex],
        urls: imageUrls
      });
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _easycom_u_rate2 = common_vendor.resolveComponent("u-rate");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_easycom_u_tabs2 + _component_avatar_image + _easycom_u_rate2 + _easycom_mescroll_body2)();
}
const _easycom_u_tabs = () => "../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_u_rate = () => "../../uni_modules/vk-uview-ui/components/u-rate/u-rate.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_u_rate + _easycom_mescroll_body)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabs,
      ["is-scroll"]: false,
      ["active-color"]: _ctx.appTheme.mainBg,
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e({
        a: "ba484d75-2-" + i0 + ",ba484d75-0",
        b: common_vendor.p({
          url: item.user.avatar_url,
          width: 50
        }),
        c: common_vendor.t(item.user.nick_name),
        d: "ba484d75-3-" + i0 + ",ba484d75-0",
        e: common_vendor.p({
          ["active-color"]: "#f4a213",
          current: $data.rates[item.score],
          disabled: true
        }),
        f: common_vendor.t(item.create_time),
        g: common_vendor.t(item.content),
        h: item.images.length
      }, item.images.length ? {
        i: common_vendor.f(item.images, (image, imgIdx, i1) => {
          return {
            a: image.image_url,
            b: common_vendor.o(($event) => $options.onPreviewImages(index, imgIdx), imgIdx),
            c: imgIdx
          };
        })
      } : {}, {
        j: common_vendor.f(item.orderGoods.goods_props, (props, idx, i1) => {
          return {
            a: common_vendor.t(props.group.name),
            b: common_vendor.t(props.value.name),
            c: idx
          };
        }),
        k: index
      });
    }),
    e: common_vendor.sr("mescrollRef", "ba484d75-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o($options.upCallback),
    h: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    i: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ba484d75"]]);
wx.createPage(MiniProgramPage);
