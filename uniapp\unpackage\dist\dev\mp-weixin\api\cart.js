"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "cart/list",
  total: "cart/total",
  add: "cart/add",
  update: "cart/update",
  clear: "cart/clear"
};
const list = () => {
  return utils_request_index.$http.get(api.list, {}, { load: false });
};
const total = () => {
  return utils_request_index.$http.get(api.total, {}, { load: false });
};
const add = (goodsId, goodsSkuId, goodsNum) => {
  return utils_request_index.$http.post(api.add, { goodsId, goodsSkuId, goodsNum });
};
const update = (goodsId, goodsSkuId, goodsNum) => {
  return utils_request_index.$http.post(api.update, { goodsId, goodsSkuId, goodsNum }, { isPrompt: false });
};
const clear = (cartIds = []) => {
  return utils_request_index.$http.post(api.clear, { cartIds });
};
exports.add = add;
exports.clear = clear;
exports.list = list;
exports.total = total;
exports.update = update;
