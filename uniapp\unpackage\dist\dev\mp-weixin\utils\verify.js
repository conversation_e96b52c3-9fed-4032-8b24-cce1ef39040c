"use strict";
const utils_util = require("./util.js");
const isEmpty = (value) => {
  if (utils_util.isArray(value)) {
    return value.length === 0;
  }
  if (utils_util.isObject(value)) {
    return utils_util.isEmptyObject(value);
  }
  return !value;
};
const isPhone = (str) => {
  const reg = /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/;
  return reg.test(str);
};
const isMobile = (str) => {
  const reg = /^(1[3456789]\d{9})$/;
  return reg.test(str);
};
exports.isEmpty = isEmpty;
exports.isMobile = isMobile;
exports.isPhone = isPhone;
