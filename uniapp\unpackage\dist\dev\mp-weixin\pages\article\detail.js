"use strict";
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const api_article_index = require("../../api/article/index.js");
const common_vendor = require("../../common/vendor.js");
const Shortcut = () => "../../components/shortcut/index.js";
const _sfc_main = {
  components: {
    Shortcut
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 当前文章ID
      articleId: null,
      // 加载中
      isLoading: true,
      // 当前文章详情
      detail: null
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.articleId = options.articleId;
    this.getArticleDetail();
  },
  methods: {
    // 获取文章详情
    getArticleDetail() {
      const app = this;
      app.isLoading = true;
      api_article_index.detail(app.articleId).then((result) => {
        app.detail = result.data.detail;
        app.setWxofficialShareData();
      }).finally(() => app.isLoading = false);
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const app = this;
      app.updateShareCardData({ title: app.detail.title });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const params = app.$getShareUrlParams({ articleId: app.articleId });
    return {
      title: app.detail.title,
      path: "/pages/article/detail?" + params
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const params = app.$getShareUrlParams({ articleId: app.articleId });
    return {
      title: app.detail.title,
      path: "/pages/article/detail?" + params
    };
  }
};
if (!Array) {
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _component_shortcut = common_vendor.resolveComponent("shortcut");
  (_easycom_mp_html2 + _component_shortcut)();
}
const _easycom_mp_html = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
if (!Math) {
  _easycom_mp_html();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: common_vendor.t($data.detail.title),
    c: common_vendor.t($data.detail.show_views),
    d: common_vendor.t($data.detail.view_time),
    e: common_vendor.p({
      content: $data.detail.content
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a5ebea5b"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
