"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const core_app = require("../../core/app.js");
const common_model_Store = require("../../common/model/Store.js");
const api_goods_index = require("../../api/goods/index.js");
const api_bargain_task = require("../../api/bargain/task.js");
const api_bargain_active = require("../../api/bargain/active.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const CountDown = () => "../../components/countdown/index.js";
const _sfc_main = {
  components: {
    AvatarImage,
    CountDown
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 是否正在加载中
      isLoading: true,
      taskId: void 0,
      // 砍价任务ID
      activeId: void 0,
      // 砍价活动ID
      task: {},
      // 砍价任务详情
      active: {},
      // 活动详情
      goods: {},
      // 商品详情
      goodsSkuInfo: {},
      // 商品SKU信息
      helpList: [],
      // 好友助力榜
      isCreater: false,
      // 是否为当前砍价任务的发起人
      isCut: false,
      // 当前是否已砍
      setting: {},
      // 砍价规则
      showRules: false,
      // 显示砍价规则
      disabled: false,
      // 按钮禁用状态
      showBuyBtn: false,
      // 立即购买
      showShareBtn: false,
      // 邀请好友砍价
      showCatBtn: false,
      // 帮TA砍一刀
      showOtherBtn: false
      // 查看其他砍价活动
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.taskId = options.taskId;
    this.onRefreshPage();
  },
  methods: {
    // 刷新页面数据
    onRefreshPage() {
      const app = this;
      app.isLoading = true;
      app.getTaskDetail().then((result) => {
        Promise.all([
          app.getActiveDetail(),
          app.getGoodsBasic(),
          app.getGoodsSku(),
          app.getHelpList()
        ]).then(() => {
          app.initShowBtn();
          app.setWxofficialShareData();
        }).finally(() => app.isLoading = false);
      });
    },
    // 获取砍价任务详情
    getTaskDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_bargain_task.detail(app.taskId).then((result) => {
          app.task = result.data.taskInfo;
          app.activeId = app.task.active_id;
          app.isCreater = result.data.isCreater;
          app.isCut = result.data.isCut;
          app.setting = result.data.setting;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取砍价活动详情
    getActiveDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_bargain_active.detail(app.activeId).then((result) => {
          app.active = result.data.active;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取商品信息
    getGoodsBasic() {
      const app = this;
      const goodsId = app.task.goods_id;
      return new Promise((resolve, reject) => {
        api_goods_index.basic(goodsId, false).then((result) => {
          app.goods = result.data.detail;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取商品SKU信息
    getGoodsSku() {
      const app = this;
      const goodsId = app.task.goods_id;
      const goodsSkuId = app.task.goods_sku_id;
      return new Promise((resolve, reject) => {
        api_goods_index.skuInfo(goodsId, goodsSkuId).then((result) => {
          app.goodsSkuInfo = result.data.skuInfo;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取砍价活动详情
    getHelpList() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_bargain_task.helpList(app.taskId).then((result) => {
          app.helpList = result.data.list;
          resolve(result);
        }).catch(reject);
      });
    },
    // 初始化：显示操作按钮
    initShowBtn() {
      const app = this;
      const showBuyBtn = app.isCreater && !app.task.is_buy && app.task.status && (!app.active.is_floor_buy || app.task.is_floor);
      const showCatBtn = !app.isCreater && !app.isCut && !app.task.is_floor && app.task.status;
      const showShareBtn = !showCatBtn && !app.task.is_floor && app.task.status;
      const showOtherBtn = !showBuyBtn && !showShareBtn && !showCatBtn;
      app.showBuyBtn = showBuyBtn;
      app.showCatBtn = showCatBtn;
      app.showShareBtn = showShareBtn;
      app.showOtherBtn = showOtherBtn;
    },
    // 显示砍价规则
    handleShowRules() {
      this.showRules = true;
    },
    // 立即购买
    handleBuyNow() {
      const app = this;
      app.$navTo("pages/checkout/index", {
        mode: "bargain",
        taskId: app.taskId
      });
    },
    // 帮砍一刀
    handleHelpCut() {
      const app = this;
      app.disabled = true;
      api_bargain_task.helpCut(app.taskId).then((result) => {
        app.$toast(result.message);
        setTimeout(() => app.onRefreshPage(), 1800);
      }).finally(() => app.disabled = false);
    },
    // 点击分享按钮
    handleShareBtn() {
    },
    // 复制当前页面链接
    handleCopyLink() {
      const app = this;
      app.getShareUrl().then((shareUrl) => {
        common_vendor.index.setClipboardData({
          data: shareUrl,
          success: () => app.$toast("复制链接成功，快去发送给朋友吧"),
          fail: ({ errMsg }) => app.$toast("复制失败 " + errMsg)
        });
      });
    },
    // 获取分享链接 (H5外链)
    getShareUrl() {
      const { path, query } = core_app.getCurrentPage();
      return new Promise((resolve, reject) => {
        common_model_Store.StoreModel.h5Url().then((baseUrl) => {
          const shareUrl = core_app.buildUrL(baseUrl, path, query);
          resolve(shareUrl);
        });
      });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const { active, goods } = this;
      this.updateShareCardData({
        title: active.share_title,
        desc: active.prompt_words,
        imgUrl: goods.goods_image
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const params = app.$getShareUrlParams({ taskId: app.taskId });
    return {
      title: app.active.share_title,
      path: `/pages/bargain/task?${params}`
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const params = app.$getShareUrlParams({ taskId: app.taskId });
    return {
      title: app.active.share_title,
      path: `/pages/bargain/task?${params}`
    };
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_component_avatar_image + _component_count_down + _easycom_u_modal2)();
}
const _easycom_u_modal = () => "../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  _easycom_u_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: common_vendor.o(($event) => _ctx.$navTo("pages/index/index")),
    c: common_vendor.o(($event) => $options.handleShowRules()),
    d: common_vendor.p({
      url: $data.task.user.avatar_url,
      width: 104
    }),
    e: common_vendor.t($data.task.user.nick_name),
    f: $data.active.prompt_words
  }, $data.active.prompt_words ? {
    g: common_vendor.t($data.active.prompt_words)
  } : {}, {
    h: $data.goodsSkuInfo.goods_image ? $data.goodsSkuInfo.goods_image : $data.goods.goods_image,
    i: common_vendor.t($data.goods.goods_name),
    j: common_vendor.t($data.goodsSkuInfo.stock_num),
    k: common_vendor.t($data.task.floor_price),
    l: common_vendor.t($data.task.goods_price),
    m: common_vendor.o(($event) => _ctx.$navTo("pages/bargain/goods/index", {
      activeId: $data.activeId,
      goodsId: $data.goods.goods_id
    })),
    n: $data.task.status
  }, $data.task.status ? common_vendor.e({
    o: !$data.task.is_floor
  }, !$data.task.is_floor ? {
    p: common_vendor.t($data.task.cut_money),
    q: common_vendor.t($data.task.surplus_money)
  } : {
    r: common_vendor.t($data.task.floor_price)
  }) : {}, {
    s: `${$data.task.bargain_rate}%`,
    t: $data.showBuyBtn
  }, $data.showBuyBtn ? {
    v: $data.task.is_floor ? 1 : "",
    w: common_vendor.o(($event) => $options.handleBuyNow())
  } : {}, {
    x: $data.showShareBtn
  }, $data.showShareBtn ? {
    y: common_vendor.o(($event) => $options.handleShareBtn())
  } : {}, {
    z: $data.showCatBtn
  }, $data.showCatBtn ? {
    A: common_vendor.o(($event) => $options.handleHelpCut())
  } : {}, {
    B: $data.showOtherBtn
  }, $data.showOtherBtn ? {
    C: common_vendor.o(($event) => _ctx.$navTo("pages/bargain/index"))
  } : {}, {
    D: $data.task.status
  }, $data.task.status ? {
    E: common_vendor.p({
      date: $data.active.end_time,
      separator: "zh",
      theme: "text"
    })
  } : {}, {
    F: $data.helpList.length
  }, $data.helpList.length ? {
    G: common_vendor.f($data.helpList, (help, idx, i0) => {
      return {
        a: "1c08b8df-2-" + i0,
        b: common_vendor.p({
          url: help.user.avatar_url,
          width: 70
        }),
        c: common_vendor.t(help.user.nick_name),
        d: common_vendor.t(help.cut_money),
        e: idx
      };
    })
  } : {}, {
    H: !$data.isLoading
  }, !$data.isLoading ? {
    I: common_vendor.t($data.setting.rulesDesc),
    J: common_vendor.o(($event) => $data.showRules = $event),
    K: common_vendor.p({
      title: "砍价规则",
      modelValue: $data.showRules
    })
  } : {}, {
    L: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1c08b8df"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
