"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "groupon.goods/list",
  detail: "groupon.goods/detail",
  poster: "groupon.goods/poster"
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
const detail = (grouponGoodsId, param) => {
  return utils_request_index.$http.get(api.detail, { grouponGoodsId, ...param });
};
const poster = (param) => {
  return utils_request_index.$http.get(api.poster, param);
};
exports.detail = detail;
exports.list = list;
exports.poster = poster;
