"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
const api_bargain_active = require("../../api/bargain/active.js");
const api_bargain_task = require("../../api/bargain/task.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const CountDown = () => "../../components/countdown/index.js";
const pageSize = 15;
const _sfc_main = {
  components: {
    AvatarImage,
    CountDown
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 是否正在加载中
      isLoading: true,
      // 当前tab索引
      curTab: 0,
      // 砍价会场商品列表
      activeList: core_app.getEmptyPaginateObj(),
      // 我的砍价列表
      myList: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于3条才显示无更多数据
        noMoreSize: 3
      }
    };
  },
  watch: {
    curTab(val) {
      common_vendor.index.setNavigationBarTitle({ title: val == 0 ? "砍价会场" : "我的砍价" });
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.tab) {
      this.curTab = options.tab;
    }
    this.setWxofficialShareData();
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getListData(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取列表数据(根据当前选项卡判断调用的方法)
    getListData(pageNo) {
      const apiFuc = {
        0: this.getActiveList,
        1: this.getMyList
      };
      return apiFuc[this.curTab](pageNo);
    },
    // 获取砍价活动列表
    getActiveList(pageNo) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_bargain_active.list({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.activeList.data = core_app.getMoreListData(newList, app.activeList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 获取我的砍价列表
    getMyList(pageNo) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_bargain_task.list({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.myList.data = core_app.getMoreListData(newList, app.myList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 切换当前选项卡
    onChangeTab(key = 0) {
      const app = this;
      app.curTab = key;
      app.activeList = core_app.getEmptyPaginateObj();
      app.myList = core_app.getEmptyPaginateObj();
      app.mescroll.resetUpScroll();
    },
    // 跳转到砍价商品详情页
    onTargetActive(item) {
      this.$navTo("pages/bargain/goods/index", { activeId: item.active_id, goodsId: item.goods_id });
    },
    // 跳转到砍价任务详情
    onTargetTask(taskId) {
      this.$navTo("pages/bargain/task", { taskId });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({ title: "砍价专区" });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const params = this.$getShareUrlParams();
    return {
      title: "砍价专区",
      path: `/pages/bargain/index?${params}`
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const params = this.$getShareUrlParams();
    return {
      title: "砍价专区",
      path: `/pages/bargain/index?${params}`
    };
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_component_avatar_image + _component_count_down + _easycom_mescroll_body2)();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.curTab == 0
  }, $data.curTab == 0 ? {
    b: common_vendor.f($data.activeList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.goods.goods_image,
        b: common_vendor.t(item.goods.goods_name),
        c: item.helpsCount > 0
      }, item.helpsCount > 0 ? {
        d: common_vendor.f(item.helpList, (help, hIdx, i1) => {
          return {
            a: "37c69674-1-" + i0 + "-" + i1 + ",37c69674-0",
            b: common_vendor.p({
              url: help.user.avatar_url,
              width: 36
            }),
            c: hIdx
          };
        }),
        e: common_vendor.t(item.helpsCount)
      } : {}, {
        f: common_vendor.t(item.goods.goods_price_min),
        g: common_vendor.t(item.floor_price),
        h: common_vendor.o(($event) => $options.onTargetActive(item), index),
        i: index
      });
    })
  } : {}, {
    c: $data.curTab == 1
  }, $data.curTab == 1 ? {
    d: common_vendor.f($data.myList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.goods.goods_image,
        b: common_vendor.t(item.goods.goods_name),
        c: item.status == true
      }, item.status == true ? {
        d: common_vendor.t(item.cut_money),
        e: common_vendor.t(item.surplus_money)
      } : {}, {
        f: item.is_floor
      }, item.is_floor ? {
        g: common_vendor.t(item.floor_price)
      } : {}, {
        h: item.status == true
      }, item.status == true ? {
        i: "37c69674-2-" + i0 + ",37c69674-0",
        j: common_vendor.p({
          date: item.end_time,
          separator: "colon",
          theme: "custom"
        })
      } : {}, {
        k: item.status == false
      }, item.status == false ? {
        l: common_vendor.t(item.is_buy ? "砍价成功" : "已结束")
      } : {}, {
        m: item.status == true
      }, item.status == true ? {} : {}, {
        n: common_vendor.o(($event) => $options.onTargetTask(item.task_id), index),
        o: index
      });
    })
  } : {}, {
    e: common_vendor.o(($event) => $options.onChangeTab(0)),
    f: $data.curTab == 0 ? 1 : "",
    g: common_vendor.o(($event) => $options.onChangeTab(1)),
    h: $data.curTab == 1 ? 1 : "",
    i: common_vendor.sr("mescrollRef", "37c69674-0"),
    j: common_vendor.o(_ctx.mescrollInit),
    k: common_vendor.o($options.upCallback),
    l: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    m: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-37c69674"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
