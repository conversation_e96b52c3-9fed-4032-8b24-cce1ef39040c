"use strict";
const config = require("../../config.js");
const core_config_defaultConfig = require("./defaultConfig.js");
const options = Object.assign({}, core_config_defaultConfig.defaultConfig, config.config);
const Config = {
  /**
   * 获取全部配置
   */
  all() {
    return options;
  },
  /**
   * 获取指定配置
   * @param {string} key
   * @param {mixed} def
   */
  get(key, def = void 0) {
    if (options.hasOwnProperty(key)) {
      return options[key];
    }
    console.error(`检测到不存在的配置项: ${key}`);
    return def;
  },
  /**
   * 获取当前商城ID
   */
  getStoreId() {
    return this.get("storeId");
  }
};
exports.Config = Config;
