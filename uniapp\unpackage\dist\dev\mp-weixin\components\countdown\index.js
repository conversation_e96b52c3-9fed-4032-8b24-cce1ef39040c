"use strict";
const utils_util = require("../../utils/util.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    // 截止的时间
    date: {
      type: String,
      default: ""
    },
    // 分隔符, colon为英文冒号，zh为中文
    separator: {
      type: String,
      default: "zh"
    },
    // 组件主题样式, text为纯文本，custom为带背景色
    theme: {
      type: String,
      default: "text"
    },
    // custom样式的背景色
    customBgColor: {
      type: String,
      default: "#252525"
    }
  },
  data() {
    return {
      // 倒计时数据
      dynamic: {
        day: "0",
        hou: "00",
        min: "00",
        sec: "00"
      },
      // 分隔符文案
      separatorText: {
        day: "天",
        hou: "时",
        min: "分",
        sec: "秒"
      }
    };
  },
  created() {
    this.setSeparatorText();
    this.onTime();
  },
  methods: {
    // 分隔符文案
    setSeparatorText() {
      const sText = this.separatorText;
      if (this.separator === "colon") {
        sText.day = ":";
        sText.hou = sText.min = ":";
        sText.sec = "";
      }
      this.separatorText = sText;
    },
    // 开始倒计时
    onTime(deep = 0) {
      const app = this;
      const dynamic = {};
      const newTime = (/* @__PURE__ */ new Date()).getTime();
      const endTime = new Date(utils_util.formatDate(app.date)).getTime();
      if (endTime - newTime <= 0) {
        return false;
      }
      const diffTime = (endTime - newTime) / 1e3;
      const day = parseInt(diffTime / 86400), hou = parseInt(diffTime % 86400 / 3600), min = parseInt(diffTime % 86400 % 3600 / 60), sec = parseInt(diffTime % 86400 % 3600 % 60);
      dynamic.day = day;
      dynamic.hou = app.timeFormat(hou);
      dynamic.min = app.timeFormat(min);
      dynamic.sec = app.timeFormat(sec);
      app.dynamic = dynamic;
      const isEnd = app.isEnd();
      if (isEnd) {
        deep > 0 && app.$emit("finish");
      }
      if (!isEnd) {
        setTimeout(() => {
          app.onTime(++deep);
        }, 100);
      }
    },
    // 判断倒计时是否结束
    isEnd() {
      const { dynamic } = this;
      return dynamic.day == "00" && dynamic.hou == "00" && dynamic.min == "00" && dynamic.sec == "00";
    },
    // 小于10的格式化函数
    timeFormat(value) {
      return value < 10 ? "0" + value : value;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.date
  }, $props.date ? common_vendor.e({
    b: $data.dynamic.day > 0
  }, $data.dynamic.day > 0 ? {
    c: common_vendor.t($data.dynamic.day),
    d: common_vendor.t($data.separatorText.day)
  } : {}, {
    e: common_vendor.t($data.dynamic.hou),
    f: $props.customBgColor,
    g: common_vendor.t($data.separatorText.hou),
    h: common_vendor.t($data.dynamic.min),
    i: $props.customBgColor,
    j: common_vendor.t($data.separatorText.min),
    k: common_vendor.t($data.dynamic.sec),
    l: $props.customBgColor,
    m: common_vendor.t($data.separatorText.sec),
    n: common_vendor.n(`${$props.theme}-theme`),
    o: common_vendor.n(`separator-${$props.separator}`)
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f3fae4ff"]]);
wx.createComponent(Component);
