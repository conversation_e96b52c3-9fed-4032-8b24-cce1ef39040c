"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    /**
     * 跳转到搜索页面
     */
    onTargetSearch() {
      this.$navTo("pages/search/index");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($props.params.placeholder),
    b: $props.itemStyle.textAlign,
    c: common_vendor.n($props.itemStyle.searchStyle),
    d: common_vendor.o((...args) => $options.onTargetSearch && $options.onTargetSearch(...args)),
    e: $props.params.sticky ? 1 : ""
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bb478158"]]);
wx.createComponent(Component);
