"use strict";
const common_vendor = require("../../common/vendor.js");
const api_shop = require("../../api/shop.js");
const Empty = () => "../../components/empty/index.js";
const _sfc_main = {
  components: {
    Empty
  },
  data() {
    return {
      // 正在加载中
      isLoading: true,
      // 是否授权了定位权限
      isAuthor: true,
      // 当前选择的门店ID
      selectedId: null,
      // 门店列表
      shopList: []
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ selectedId }) {
    const app = this;
    app.selectedId = selectedId ? selectedId : null;
    app.getShopList();
    app.getLocation((res) => {
      app.getShopList(res.longitude, res.latitude);
    });
  },
  methods: {
    // 获取门店列表
    getShopList(longitude, latitude) {
      const app = this;
      app.isLoading = true;
      api_shop.list({
        isCheck: 1,
        longitude: longitude ? longitude : "",
        latitude: latitude ? latitude : ""
      }).then((result) => app.shopList = result.data.list).finally(() => app.isLoading = false);
    },
    // 获取用户坐标
    // 参考文档：https://uniapp.dcloud.io/api/location/location?id=getlocation
    getLocation(callback) {
      const app = this;
      common_vendor.index.getLocation({
        type: "wgs84",
        success: callback,
        fail() {
          app.$toast("获取定位失败，请点击右下角按钮重新尝试定位");
          app.isAuthor = false;
        }
      });
    },
    // 授权启用定位权限
    onAuthorize() {
      const app = this;
      common_vendor.index.openSetting({
        success(res) {
          if (res.authSetting["scope.userLocation"]) {
            console.log("定位权限授权成功");
            app.isAuthor = true;
            setTimeout(() => {
              app.getLocation((res2) => {
                app.getShopList(res2.longitude, res2.latitude);
              });
            }, 1e3);
          }
        }
      });
    },
    /**
     * 选择门店
     */
    onSelectedShop(selectedId) {
      const app = this;
      app.selectedId = selectedId;
      common_vendor.index.$emit("syncSelectedId", selectedId);
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
  }
};
if (!Array) {
  const _component_empty = common_vendor.resolveComponent("empty");
  _component_empty();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.shopList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.shop_name),
        b: common_vendor.t(item.region.province),
        c: common_vendor.t(item.region.city),
        d: common_vendor.t(item.region.region),
        e: common_vendor.t(item.address),
        f: common_vendor.t(item.phone),
        g: item.distance
      }, item.distance ? {
        h: common_vendor.t(item.distance_unit)
      } : {}, {
        i: item.shop_id == $data.selectedId
      }, item.shop_id == $data.selectedId ? {} : {}, {
        j: index,
        k: common_vendor.o(($event) => $options.onSelectedShop(item.shop_id), index)
      });
    }),
    b: !$data.isAuthor
  }, !$data.isAuthor ? {
    c: common_vendor.o(($event) => $options.onAuthorize())
  } : {}, {
    d: !$data.shopList.length
  }, !$data.shopList.length ? {
    e: common_vendor.p({
      isLoading: $data.isLoading,
      tips: "亲，暂无自提门店哦"
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0340a2b9"]]);
wx.createPage(MiniProgramPage);
