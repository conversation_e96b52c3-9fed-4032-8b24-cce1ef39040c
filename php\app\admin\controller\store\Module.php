<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\controller\store;

use think\response\Json;
use think\db\exception\DbException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use app\admin\controller\Controller;
use app\admin\model\store\Module as ModuleModel;

/**
 * 商家后台功能模块控制器
 * Class Module
 * @package app\store\controller
 */
class Module extends Controller
{
    /**
     * 获取系统默认的功能模块
     * @return Json
     */
    public function default(): Json
    {
        $default = ModuleModel::getDefaultConfig();
        return $this->renderSuccess(compact('default'));
    }

    /**
     * 获取指定商家的功能模块
     * @param int $storeId
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function detail(int $storeId): Json
    {
        $model = new ModuleModel;
        $moduleKeys = $model->getConfigValue($storeId);
        return $this->renderSuccess(compact('moduleKeys'));
    }

    /**
     * 更新指定商家的功能模块
     * @param int $storeId
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit(int $storeId): Json
    {
        $model = ModuleModel::getDetail($storeId);
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }
}
