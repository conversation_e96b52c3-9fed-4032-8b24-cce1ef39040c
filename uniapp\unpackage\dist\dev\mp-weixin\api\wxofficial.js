"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  jssdkConfig: "wxofficial/jssdkConfig",
  oauthUrl: "wxofficial/oauthUrl",
  oauthUserInfo: "wxofficial/oauthUserInfo"
};
const jssdkConfig = (url) => {
  return utils_request_index.$http.get(api.jssdkConfig, { url });
};
const oauthUrl = (callbackUrl) => {
  return utils_request_index.$http.get(api.oauthUrl, { callbackUrl });
};
const oauthUserInfo = (code) => {
  return utils_request_index.$http.get(api.oauthUserInfo, { code });
};
const Api = { jssdkConfig, oauthUrl, oauthUserInfo };
exports.Api = Api;
