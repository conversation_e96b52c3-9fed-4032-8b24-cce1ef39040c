"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  order: "vip.checkout/order",
  payed: "vip.checkout/payed",
  submit: "vip.checkout/submit"
};
const payed = (orderId, param) => {
  return utils_request_index.$http.get(api.payed, { orderId, ...param });
};
const order = (mode, param) => {
  return utils_request_index.$http.get(api.order, { mode, ...param });
};
const submit = (mode, data) => {
  return utils_request_index.$http.post(api.submit, { mode, ...data }, { isPrompt: false });
};
const CheckoutApi = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  order,
  payed,
  submit
}, Symbol.toStringTag, { value: "Module" }));
exports.CheckoutApi = CheckoutApi;
