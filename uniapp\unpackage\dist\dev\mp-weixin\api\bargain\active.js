"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "bargain.active/list",
  detail: "bargain.active/detail",
  poster: "bargain.active/poster"
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
const detail = (activeId, param) => {
  return utils_request_index.$http.get(api.detail, { activeId, ...param });
};
const poster = (param) => {
  return utils_request_index.$http.get(api.poster, param);
};
exports.detail = detail;
exports.list = list;
exports.poster = poster;
