"use strict";
const store_index = require("../../store/index.js");
const core_config_index = require("../../core/config/index.js");
const utils_storage = require("../../utils/storage.js");
const api_setting = require("../../api/setting.js");
const common_enum_setting_Key = require("../enum/setting/Key.js");
require("../../utils/request/index.js");
const core_platform = require("../../core/platform.js");
require("../vendor.js");
const CACHE_KEY = "Setting";
const setStorage = (data2) => {
  const expireTime = 10 * 60;
  utils_storage.storage.set(CACHE_KEY, data2, expireTime);
};
const getStorage = () => {
  return utils_storage.storage.get(CACHE_KEY);
};
const getApiData = () => {
  return new Promise((resolve, reject) => {
    api_setting.data().then((result) => {
      resolve(result.data.setting);
    });
  });
};
const data = (isCache = void 0) => {
  if (isCache == void 0) {
    isCache = core_config_index.Config.get("enabledSettingCache");
  }
  return new Promise((resolve, reject) => {
    const cacheData = getStorage();
    if (isCache && cacheData) {
      resolve(cacheData);
    } else {
      getApiData().then((data2) => {
        setStorage(data2);
        resolve(data2);
      });
    }
  });
};
const item = (key, isCache = void 0) => {
  return new Promise((resolve, reject) => {
    data(isCache).then((setting) => resolve(setting[key]));
  });
};
const setAppTheme = () => {
  return new Promise((resolve, reject) => {
    item(common_enum_setting_Key.SettingKeyEnum.APP_THEME.value).then((appTheme) => {
      store_index.store.dispatch("SetAppTheme", appTheme);
      resolve();
    });
  });
};
const isShowCustomerBtn = async () => {
  const setting = await item(common_enum_setting_Key.SettingKeyEnum.CUSTOMER.value, true);
  if (!setting.enabled) {
    return false;
  }
  return setting.provider === "wxqykf" || setting.provider === "mpwxkf" && core_platform.platfrom === "MP-WEIXIN";
};
const SettingModel = {
  setStorage,
  data,
  item,
  setAppTheme,
  isShowCustomerBtn
};
exports.SettingModel = SettingModel;
