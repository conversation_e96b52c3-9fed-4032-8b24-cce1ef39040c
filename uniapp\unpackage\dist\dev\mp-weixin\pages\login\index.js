"use strict";
const common_enum_setting_Key = require("../../common/enum/setting/Key.js");
const common_model_Setting = require("../../common/model/Setting.js");
const common_vendor = require("../../common/vendor.js");
const Main = () => "./components/main.js";
const MpWeixin = () => "./components/mp-weixin.js";
const MpAlipay = () => "./components/mp-alipay.js";
const WxOfficial = () => "./components/wx-official.js";
const _sfc_main = {
  components: {
    Main,
    MpWeixin,
    MpAlipay,
    WxOfficial
  },
  data() {
    return {
      // 数据加载完成 [防止在微信小程序端onLoad和view渲染同步进行]
      isLoad: false,
      // 注册设置 (后台设置)
      setting: {},
      // 是否显示微信小程序授权登录
      isMpWeixinAuth: false,
      // 是否显示微信小程序端 一键授权手机号
      isMpWeixinMobile: false,
      // 是否显示微信公众号授权登录
      isWxOfficialAuth: false,
      // 是否显示支付宝小程序授权登录
      isMpAlipayAuth: false,
      // 是否存在第三方用户信息
      isParty: true,
      // 第三方用户信息数据
      partyData: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await this.getRegisterSetting();
    await this.setShowUserInfo();
    this.isLoad = true;
  },
  methods: {
    // 获取注册设置 [后台-客户端-注册设置]
    async getRegisterSetting() {
      this.setting = await common_model_Setting.SettingModel.item(common_enum_setting_Key.SettingKeyEnum.REGISTER.value, false);
    },
    /**
     * 设置当前是否显示第三方授权登录
     *  - 条件1: 只有对应的客户端显示获取用户信息按钮, 例如微信小程序、微信公众号
     *  - 条件2: 注册设置是否已开启该选项
     */
    async setShowUserInfo() {
      const app = this;
      const isMpWeixin = app.platform === "MP-WEIXIN";
      const isWxOfficial = app.platform === "WXOFFICIAL";
      const isMpAlipay = app.platform === "MP-ALIPAY" && my.canIUse("getAuthCode");
      app.isMpWeixinMobile = isMpWeixin && Boolean(app.setting.isOauthMobileMpweixin);
      app.isWxOfficialAuth = isWxOfficial && Boolean(app.setting.isOauthWxofficial);
      app.isMpAlipayAuth = isMpAlipay && Boolean(app.setting.isOauthMpAlipay);
    },
    // 获取到用户信息的回调函数
    onGetUserInfoSuccess(result) {
      this.partyData = result;
      console.log(66);
      console.log();
      this.onShowRegister();
    },
    // 显示注册页面
    onShowRegister() {
      if (this.partyData.oauth === "MP-WEIXIN") {
        this.isMpWeixinAuth = false;
      }
      if (this.partyData.oauth === "WXOFFICIAL") {
        this.isWxOfficialAuth = false;
      }
      if (this.partyData.oauth === "MP-ALIPAY") {
        this.isMpAlipayAuth = false;
      }
      this.isParty = true;
    }
  }
};
if (!Array) {
  const _component_MpWeixin = common_vendor.resolveComponent("MpWeixin");
  const _component_Main = common_vendor.resolveComponent("Main");
  (_component_MpWeixin + _component_Main)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoad
  }, $data.isLoad ? common_vendor.e({
    b: $data.isMpWeixinAuth
  }, $data.isMpWeixinAuth ? {
    c: common_vendor.o($options.onGetUserInfoSuccess)
  } : {
    d: common_vendor.o($options.onGetUserInfoSuccess),
    e: common_vendor.p({
      partyData: $data.partyData,
      isMpWeixinMobile: $data.isMpWeixinMobile
    })
  }, {
    f: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
