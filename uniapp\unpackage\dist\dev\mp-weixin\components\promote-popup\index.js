"use strict";
const core_app = require("../../core/app.js");
const utils_storage = require("../../utils/storage.js");
const api_promote = require("../../api/promote.js");
const common_enum_market_promote_ContentType = require("../../common/enum/market/promote/ContentType.js");
const common_enum_market_promote_CloseMode = require("../../common/enum/market/promote/CloseMode.js");
const common_enum_market_promote_Frequency = require("../../common/enum/market/promote/Frequency.js");
const utils_util = require("../../utils/util.js");
const common_vendor = require("../../common/vendor.js");
const LocalIndex = "PromoteViews";
const _sfc_main = {
  props: {},
  data() {
    return {
      // 枚举类
      ContentTypeEnum: common_enum_market_promote_ContentType.ContentTypeEnum,
      CloseModeEnum: common_enum_market_promote_CloseMode.CloseModeEnum,
      FrequencyEnum: common_enum_market_promote_Frequency.FrequencyEnum,
      // 弹窗显示隐藏
      show: false,
      // 倒计时时长(秒)
      times: 0,
      // 定时器句柄
      interHandle: void 0,
      // 当前展现的活动索引
      current: 0,
      // 开屏推广活动列表
      activeList: []
    };
  },
  computed: {
    // 当前展现的活动详情
    currentActive() {
      return this.activeList.length ? this.activeList[this.current] : null;
    }
  },
  created() {
    setTimeout(() => {
      this.$checkModule("market-promote") && this.getActiveList();
    }, 1e3);
  },
  methods: {
    // 获取开屏推广活动
    async getActiveList() {
      const app = this;
      const result = await api_promote.activeList();
      app.activeList = app.getUsableList(result.data.list);
      app.activeList.length > 0 && app.showPromote(0);
    },
    // 展现弹窗
    showPromote(index = 0) {
      const app = this;
      app.show = true;
      app.current = index;
      if (app.currentActive.close_mode == common_enum_market_promote_CloseMode.CloseModeEnum.AUTO.value) {
        app.startTimer(app.currentActive.seconds);
      }
      app.setViewsState();
    },
    // 整理允许展现的活动列表
    getUsableList(list) {
      const localData = utils_storage.storage.get(LocalIndex);
      const todayTime = new Date((/* @__PURE__ */ new Date()).toLocaleDateString()).getTime();
      return list.filter((item) => {
        const currentPage = core_app.getCurrentPage();
        if (!utils_util.inArray(currentPage.path, item.pages)) {
          return false;
        }
        const localItem = localData[item.promote_id];
        if (!localItem) {
          return true;
        }
        if (item.frequency == common_enum_market_promote_Frequency.FrequencyEnum.ONCE.value) {
          return false;
        }
        if (item.frequency == common_enum_market_promote_Frequency.FrequencyEnum.EVERY_DAY.value && todayTime > localItem.showTime) {
          return true;
        }
        return false;
      });
    },
    // 手动关闭弹窗
    handleClose() {
      this.show = false;
      this.endTimer();
      const next = this.current + 1;
      if (this.activeList.length > next) {
        setTimeout(() => this.showPromote(next), 1e3);
      }
    },
    // 执行定时器 (倒计时关闭弹窗)
    startTimer(times) {
      const app = this;
      app.times = times;
      app.interHandle = setInterval(() => {
        app.times--;
        if (app.times <= 0) {
          app.handleClose();
        }
      }, 1e3);
    },
    // 结束定时器
    endTimer() {
      this.times = 0;
      clearInterval(this.interHandle);
    },
    // 记录活动展现状态
    setViewsState() {
      const { currentActive } = this;
      const data = utils_storage.storage.get(LocalIndex) || {};
      utils_storage.storage.set(LocalIndex, {
        ...data,
        [currentActive.promote_id]: {
          // promoteId: currentActive.promote_id,
          // frequency: currentActive.frequency,
          showTime: (/* @__PURE__ */ new Date()).getTime()
          // 展现的时间
        }
      });
      this.updateViewsNum(currentActive.promote_id);
    },
    // 单图广告点击事件
    handleSingleItem() {
      const { currentActive } = this;
      this.updateClickNum(currentActive.promote_id);
      core_app.onLink(currentActive.content_config[common_enum_market_promote_ContentType.ContentTypeEnum.SINGLE.value].link);
      this.handleClose();
    },
    // 轮播图点击事件
    handleSwiperItem(index) {
      const { currentActive } = this;
      this.updateClickNum(currentActive.promote_id);
      core_app.onLink(currentActive.content_config[common_enum_market_promote_ContentType.ContentTypeEnum.SWIPER.value].adList[index].link);
    },
    // 请求后端累积浏览次数
    updateViewsNum(promoteId) {
      api_promote.updateViewsNum(promoteId);
    },
    // 请求后端累积点击次数
    updateClickNum(promoteId) {
      api_promote.updateClickNum(promoteId);
    }
  }
};
if (!Array) {
  const _easycom_u_swiper2 = common_vendor.resolveComponent("u-swiper");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_mask2 = common_vendor.resolveComponent("u-mask");
  (_easycom_u_swiper2 + _easycom_u_icon2 + _easycom_u_mask2)();
}
const _easycom_u_swiper = () => "../../uni_modules/vk-uview-ui/components/u-swiper/u-swiper.js";
const _easycom_u_icon = () => "../../uni_modules/vk-uview-ui/components/u-icon/u-icon.js";
const _easycom_u_mask = () => "../../uni_modules/vk-uview-ui/components/u-mask/u-mask.js";
if (!Math) {
  (_easycom_u_swiper + _easycom_u_icon + _easycom_u_mask)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.show
  }, $data.show ? common_vendor.e({
    b: $options.currentActive.content_type == $data.ContentTypeEnum.SINGLE.value
  }, $options.currentActive.content_type == $data.ContentTypeEnum.SINGLE.value ? {
    c: $options.currentActive.content_config[$data.ContentTypeEnum.SINGLE.value].imageUrl,
    d: common_vendor.o(($event) => $options.handleSingleItem())
  } : {}, {
    e: $options.currentActive.content_type == $data.ContentTypeEnum.SWIPER.value
  }, $options.currentActive.content_type == $data.ContentTypeEnum.SWIPER.value ? {
    f: common_vendor.o($options.handleSwiperItem),
    g: common_vendor.p({
      ["bg-color"]: "unset",
      name: "imageUrl",
      list: $options.currentActive.content_config[$data.ContentTypeEnum.SWIPER.value].adList,
      interval: 3e3,
      effect3d: true,
      height: 640
    })
  } : {}, {
    h: common_vendor.p({
      name: "close-circle",
      effect3d: true
    }),
    i: common_vendor.o(($event) => $options.handleClose()),
    j: $options.currentActive.close_mode == $data.CloseModeEnum.AUTO.value
  }, $options.currentActive.close_mode == $data.CloseModeEnum.AUTO.value ? {
    k: common_vendor.t($data.times)
  } : {}) : {}, {
    l: common_vendor.p({
      show: $data.show,
      duration: 100,
      zoom: false
    })
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5ac4c1c3"]]);
wx.createComponent(Component);
