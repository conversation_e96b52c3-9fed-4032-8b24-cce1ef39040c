"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  login: "passport/login",
  loginMpWx: "passport/loginMpWx",
  loginWxOfficial: "passport/loginWxOfficial",
  loginMpWxMobile: "passport/loginMpWxMobile",
  loginMpAlipay: "passport/loginMpAlipay",
  isPersonalMpweixin: "passport/isPersonalMpweixin"
};
function login(data) {
  return utils_request_index.$http.post(api.login, data);
}
function loginMpWx(data, option) {
  return utils_request_index.$http.post(api.loginMpWx, data, option);
}
function isPersonalMpweixin(data, option) {
  return utils_request_index.$http.post(api.isPersonalMpweixin, data, option);
}
function loginWxOfficial(data, option) {
  return utils_request_index.$http.post(api.loginWxOfficial, data, option);
}
function loginMpWxMobile(data, option) {
  return utils_request_index.$http.post(api.loginMpWxMobile, data, option);
}
function loginMpAlipay(data, option) {
  return utils_request_index.$http.post(api.loginMpAlipay, data, option);
}
exports.isPersonalMpweixin = isPersonalMpweixin;
exports.login = login;
exports.loginMpAlipay = loginMpAlipay;
exports.loginMpWx = loginMpWx;
exports.loginMpWxMobile = loginMpWxMobile;
exports.loginWxOfficial = loginWxOfficial;
