"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_xj_video = require("../../api/xj/video.js");
const api_xj_category = require("../../api/xj/category.js");
const core_app = require("../../core/app.js");
const Search = () => "../../components/search/index.js";
const pageSize = 10;
const showViewKey = "GoodsList-ShowView";
const _sfc_main = {
  components: {
    Search
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      current: 0,
      showView: false,
      // 列表显示方式 (true列表、false平铺)
      sortType: "all",
      // 排序类型
      sortPrice: false,
      // 价格排序 (true高到低 false低到高)
      options: {},
      // 当前页面参数
      list: core_app.getEmptyPaginateObj(),
      // 商品列表数据
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: {
          size: pageSize
        },
        // 数量要大于4条才显示无更多数据
        noMoreSize: 4
      },
      categoryList: []
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    this.getCategoryList();
    this.setShowView();
  },
  onShow() {
    this.isLogin = core_app.checkLogin();
    !this.isLogin && this.$navTo("pages/login/index");
  },
  methods: {
    qiehuan(category_id) {
      this.current = category_id;
      this.list = core_app.getEmptyPaginateObj();
      this.mescroll.resetUpScroll();
    },
    getCategoryList() {
      let app = this;
      api_xj_category.list().then((result) => {
        app.categoryList = result.data.list;
      });
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getGoodsList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 设置默认列表显示方式
    setShowView() {
      this.showView = common_vendor.index.getStorageSync(showViewKey) || false;
    },
    /**
     * 获取商品列表
     * @param {number} pageNo 页码
     */
    getGoodsList(pageNo = 1) {
      const app = this;
      console.log(app.options);
      const param = {
        sortType: app.sortType,
        sortPrice: Number(app.sortPrice),
        categoryId: app.current,
        goodsName: app.options.search || "",
        page: pageNo
      };
      return new Promise((resolve, reject) => {
        api_xj_video.list(param).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 切换排序方式
    handleSortType(newSortType) {
      const app = this;
      const newSortPrice = newSortType === "price" ? !app.sortPrice : true;
      app.sortType = newSortType;
      app.sortPrice = newSortPrice;
      app.list = core_app.getEmptyPaginateObj();
      app.mescroll.resetUpScroll();
    },
    // 切换列表显示方式
    handleShowView() {
      const app = this;
      app.showView = !app.showView;
      common_vendor.index.setStorageSync(showViewKey, app.showView);
    },
    // 跳转商品详情页
    onTargetDetail(articleId) {
      var categoryId = this.current;
      common_vendor.index.navigateTo({
        url: "/pages/video/detail?articleId=" + articleId + "&categoryId=" + categoryId
      });
    }
  },
  /**
   * 设置分享内容
   */
  onShareAppMessage() {
    const app = this;
    return {
      title: "视频列表",
      path: "/pages/video/index?" + this.$getShareUrlParams(app.options)
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    return {
      title: "视频列表",
      path: "/pages/video/index?" + this.$getShareUrlParams(app.options)
    };
  }
};
if (!Array) {
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  _easycom_mescroll_body2();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.n($data.current == 0 ? "active" : ""),
    b: common_vendor.o(($event) => $options.qiehuan(0)),
    c: common_vendor.f($data.categoryList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.o(($event) => $options.qiehuan(item.category_id)),
        c: common_vendor.n($data.current == item.category_id ? "active" : "")
      };
    }),
    d: _ctx.scrollLeft,
    e: common_vendor.o((...args) => _ctx.scroll && _ctx.scroll(...args)),
    f: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e($data.showView ? {
        a: item.goods_image,
        b: common_vendor.t(item.goods_name)
      } : {
        c: item.image_url,
        d: common_vendor.t(item.title)
      }, {
        e: index,
        f: common_vendor.o(($event) => $options.onTargetDetail(item.id), index)
      });
    }),
    g: $data.showView,
    h: common_vendor.n("column-" + ($data.showView ? "1" : "2")),
    i: common_vendor.sr("mescrollRef", "2b676d09-0"),
    j: common_vendor.o(_ctx.mescrollInit),
    k: common_vendor.o(_ctx.downCallback),
    l: common_vendor.o($options.upCallback),
    m: common_vendor.p({
      sticky: true,
      down: {
        native: true
      },
      up: $data.upOption
    }),
    n: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2b676d09"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
