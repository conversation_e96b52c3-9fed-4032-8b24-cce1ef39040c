"use strict";
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../../../core/app.js");
const api_goods_index = require("../../../../api/goods/index.js");
const common_vendor = require("../../../../common/vendor.js");
const AddCartBtn = () => "../../../../components/add-cart-btn/index.js";
const AddCartPopup = () => "../../../../components/add-cart-popup/index.js";
const pageSize = 15;
const _sfc_main = {
  components: {
    AddCartBtn,
    AddCartPopup
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  props: {
    // 分类列表
    list: {
      type: Array,
      default: []
    },
    // 分类设置
    setting: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      // 一级分类：指针
      curIndex: 0,
      // 是否显示子分类
      showSubCate: false,
      // 二级分类：指针
      curIndex2: 0,
      // 商品列表
      goodsList: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于3条才显示无更多数据
        noMoreSize: 3,
        // 返回顶部
        toTop: { right: 30, bottom: 48, zIndex: 9 }
      }
    };
  },
  computed: {
    // 二级分类列表
    subCateList() {
      if (this.list[this.curIndex] && this.list[this.curIndex].children) {
        return this.list[this.curIndex].children;
      }
      return [];
    }
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      setTimeout(() => {
        app.getGoodsList(page.num).then((list) => {
          const curPageLen = list.data.length;
          const totalSize = list.data.total;
          app.mescroll.endBySize(curPageLen, totalSize);
        }).catch(() => app.mescroll.endErr());
      }, 500);
    },
    /**
     * 获取商品列表
     * @param {Number} pageNo 页码
     */
    getGoodsList(pageNo = 1) {
      const app = this;
      const categoryId = app.getCategoryId();
      return new Promise((resolve, reject) => {
        api_goods_index.list({ categoryId, page: pageNo, type: 0 }, { load: false }).then((result) => {
          const newList = result.data.list;
          app.goodsList.data = core_app.getMoreListData(newList, app.goodsList, pageNo);
          app.goodsList.last_page = newList.last_page;
          resolve(newList);
        }).catch(reject);
      });
    },
    // 获取当前选择的分类ID
    getCategoryId() {
      const app = this;
      if (app.curIndex2 > -1) {
        console.log(88);
        console.log(app.list);
        return app.subCateList[app.curIndex2].category_id;
      }
      return app.curIndex > -1 ? app.list[app.curIndex].category_id : 0;
    },
    // 一级分类：选中分类
    handleSelectNav(index) {
      this.curIndex = index;
      this.onRefreshList();
      this.showSubCate = false;
      this.curIndex2 = -1;
    },
    // 二级分类：选中分类
    handleSelectSubCate(index) {
      this.curIndex2 = index;
      this.showSubCate = false;
      this.onRefreshList();
    },
    // 刷新列表数据
    onRefreshList() {
      this.goodsList = core_app.getEmptyPaginateObj();
      setTimeout(() => this.mescroll.resetUpScroll(), 120);
    },
    // 跳转至商品列表页
    onTargetGoods(goodsId) {
      this.$navTo("pagesNew/points/detail", { goodsId });
    },
    // 点击加入购物车
    handleAddCart(item) {
      this.$refs.AddCartPopup.handle(item);
    },
    // 更新购物车角标
    onUpdateCartTabBadge() {
      console.log("onUpdateCartTabBadge");
      core_app.setCartTabBadge();
    },
    // 切换子分类显示状态
    handleShowSubCate() {
      this.showSubCate = !this.showSubCate;
    }
  }
};
if (!Array) {
  const _component_AddCartPopup = common_vendor.resolveComponent("AddCartPopup");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_component_AddCartPopup + _easycom_mescroll_body2)();
}
const _easycom_mescroll_body = () => "../../../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($options.subCateList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: $data.curIndex2 == index ? 1 : "",
        c: index,
        d: common_vendor.o(($event) => $options.handleSelectSubCate(index), index)
      };
    }),
    b: common_vendor.o(() => {
    }),
    c: common_vendor.f($data.goodsList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.goods_image,
        b: common_vendor.t(item.goods_name),
        c: common_vendor.t(Number(item.goods_price_min)),
        d: item.line_price_min > 0
      }, item.line_price_min > 0 ? {
        e: common_vendor.t(item.line_price_min)
      } : {}, {
        f: common_vendor.o(($event) => $options.onTargetGoods(item.goods_id), index),
        g: index
      });
    }),
    d: $data.showSubCate,
    e: common_vendor.o(() => {
    }),
    f: common_vendor.o((...args) => $options.handleShowSubCate && $options.handleShowSubCate(...args)),
    g: common_vendor.sr("AddCartPopup", "1972a07d-1,1972a07d-0"),
    h: common_vendor.o($options.onUpdateCartTabBadge),
    i: common_vendor.sr("mescrollRef", "1972a07d-0"),
    j: common_vendor.o(_ctx.mescrollInit),
    k: common_vendor.o($options.upCallback),
    l: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption,
      bottombar: false
    }),
    m: common_vendor.s(_ctx.appThemeStyle)
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1972a07d"]]);
wx.createComponent(Component);
