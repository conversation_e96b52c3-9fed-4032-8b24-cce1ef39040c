"use strict";
const utils_util = require("../../../../utils/util.js");
const common_enum_sharp_ActiveStatus = require("../../../../common/enum/sharp/ActiveStatus.js");
const common_enum_sharp_GoodsStatus = require("../../../../common/enum/sharp/GoodsStatus.js");
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const CountDown = () => "../../../countdown/index.js";
const _sfc_main = {
  components: {
    CountDown
  },
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    data: Object
  },
  data() {
    return {
      // 枚举类
      ActiveStatusEnum: common_enum_sharp_ActiveStatus.ActiveStatusEnum,
      GoodsStatusEnum: common_enum_sharp_GoodsStatus.GoodsStatusEnum,
      // 公共函数
      inArray: utils_util.inArray
    };
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 跳转到秒杀会场页
    handleNavMore() {
      this.$navTo("pages/sharp/index");
    },
    // 跳转到秒杀商品详情页
    handleNavDetail(sharpGoodsId) {
      const { data } = this;
      this.$navTo("pages/sharp/goods/index", {
        activeTimeId: data.active.active_time_id,
        sharpGoodsId
      });
    }
  }
};
if (!Array) {
  const _component_count_down = common_vendor.resolveComponent("count-down");
  _component_count_down();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.data.goodsList.data && $props.data.goodsList.data.length
  }, $props.data.goodsList.data && $props.data.goodsList.data.length ? common_vendor.e({
    b: common_vendor.t($props.data.active.sharp_modular_text),
    c: $props.data.active.status == $data.GoodsStatusEnum.STATE_BEGIN.value
  }, $props.data.active.status == $data.GoodsStatusEnum.STATE_BEGIN.value ? {
    d: common_vendor.p({
      date: $props.data.active.count_down_time,
      separator: "colon",
      theme: "custom"
    })
  } : {}, {
    e: common_vendor.o(($event) => $options.handleNavMore()),
    f: common_vendor.f($props.data.goodsList.data, (goods, idx, i0) => {
      return common_vendor.e($props.itemStyle.column == 1 ? {} : common_vendor.e({
        a: goods.goods_image,
        b: $data.inArray("goodsName", $props.itemStyle.show)
      }, $data.inArray("goodsName", $props.itemStyle.show) ? {
        c: common_vendor.t(goods.goods_name)
      } : {}, {
        d: $data.inArray("seckillPrice", $props.itemStyle.show)
      }, $data.inArray("seckillPrice", $props.itemStyle.show) ? {
        e: common_vendor.t(goods.seckill_price_min)
      } : {}, {
        f: $data.inArray("originalPrice", $props.itemStyle.show) && goods.original_price > 0
      }, $data.inArray("originalPrice", $props.itemStyle.show) && goods.original_price > 0 ? {
        g: common_vendor.t(goods.original_price)
      } : {}), {
        h: idx,
        i: common_vendor.o(($event) => $options.handleNavDetail(goods.sharp_goods_id), idx)
      });
    }),
    g: $props.itemStyle.column == 1,
    h: common_vendor.n(`column__${$props.itemStyle.column}`),
    i: $props.itemStyle.background
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-75512425"]]);
wx.createComponent(Component);
