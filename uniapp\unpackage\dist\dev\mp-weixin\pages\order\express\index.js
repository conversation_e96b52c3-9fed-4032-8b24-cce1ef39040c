"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_order = require("../../../api/order.js");
const _sfc_main = {
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 当前标签索引
      curTab: 0,
      // 当前订单ID
      orderId: null,
      // 物流信息
      express: {}
    };
  },
  computed: {
    tabs() {
      if (this.express && this.express.length) {
        return this.express.map((item, index) => {
          return { name: `包裹${index + 1}` };
        });
      }
      return [];
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ orderId }) {
    this.orderId = orderId;
    this.getExpress();
  },
  methods: {
    // 获取当前订单的物流信息
    getExpress() {
      const app = this;
      app.isLoading = true;
      api_order.express(app.orderId).then((result) => {
        app.express = result.data.express;
        app.isLoading = false;
      });
    },
    // 复制指定内容
    handleCopy(value) {
      const app = this;
      common_vendor.index.setClipboardData({
        data: value,
        success: () => app.$toast("复制成功"),
        fail: ({ errMsg }) => app.$toast("复制失败 " + errMsg)
      });
    },
    // 切换标签项
    onChangeTab(index) {
      this.curTab = index;
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  _easycom_u_tabs2();
}
const _easycom_u_tabs = () => "../../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
if (!Math) {
  _easycom_u_tabs();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading && $data.express.length
  }, !$data.isLoading && $data.express.length ? common_vendor.e({
    b: $options.tabs.length > 1
  }, $options.tabs.length > 1 ? {
    c: common_vendor.o($options.onChangeTab),
    d: common_vendor.o(($event) => $data.curTab = $event),
    e: common_vendor.p({
      list: $options.tabs,
      isScroll: true,
      ["active-color"]: _ctx.appTheme.mainBg,
      duration: 0.2,
      ["bar-width"]: "60",
      modelValue: $data.curTab
    })
  } : {}, {
    f: common_vendor.f($data.express[$data.curTab].goods, (goods, idx, i0) => {
      return {
        a: goods.goods.goods_image,
        b: common_vendor.t(goods.delivery_num),
        c: idx
      };
    }),
    g: $options.tabs.length > 1,
    h: $data.express[$data.curTab].delivery_method == 20
  }, $data.express[$data.curTab].delivery_method == 20 ? {} : {
    i: common_vendor.t($data.express[$data.curTab].express ? $data.express[$data.curTab].express.express_name : "--")
  }, {
    j: common_vendor.t($data.express[$data.curTab].express_no ? $data.express[$data.curTab].express_no : "--"),
    k: $data.express[$data.curTab].express_no,
    l: common_vendor.o(($event) => $options.handleCopy($data.express[$data.curTab].express_no)),
    m: $data.express[$data.curTab].traces && $data.express[$data.curTab].traces.length
  }, $data.express[$data.curTab].traces && $data.express[$data.curTab].traces.length ? {
    n: common_vendor.f($data.express[$data.curTab].traces, (item, index, i0) => {
      return {
        a: common_vendor.t(item.context),
        b: common_vendor.t(item.time),
        c: index === 0 ? 1 : "",
        d: index
      };
    })
  } : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b1133573"]]);
wx.createPage(MiniProgramPage);
