"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  detail: "shop.order/detail",
  extract: "shop.order/extract"
};
function detail(orderId, param) {
  return utils_request_index.$http.get(api.detail, { orderId, ...param });
}
function extract(orderId, param) {
  return utils_request_index.$http.post(api.extract, { orderId, ...param });
}
exports.detail = detail;
exports.extract = extract;
