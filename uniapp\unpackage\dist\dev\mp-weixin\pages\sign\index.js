"use strict";
const common_vendor = require("../../common/vendor.js");
const api_xj_sign = require("../../api/xj/sign.js");
if (!Array) {
  const _easycom_cy_qiandao2 = common_vendor.resolveComponent("cy-qiandao");
  _easycom_cy_qiandao2();
}
const _easycom_cy_qiandao = () => "../../uni_modules/cy-qiandao/components/cy-qiandao/cy-qiandao.js";
if (!Math) {
  _easycom_cy_qiandao();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const qiandaolist = common_vendor.ref([]);
    const qiandaodays = common_vendor.ref("");
    const lianxuqiandaodays = common_vendor.ref("");
    const isLoading = common_vendor.ref("");
    common_vendor.onLoad(() => {
      getqiandao();
    });
    function getqiandao() {
      api_xj_sign.list().then((result) => {
        qiandaolist.value = result.data.list;
        qiandaodays.value = qiandaolist.value.length;
        isLoading.value = 1;
        iscontinuous();
        console.log(result.data.list);
      });
    }
    function iscontinuous() {
      let qiandao2 = qiandaolist.value;
      qiandao2 = qiandao2.sort();
      qiandao2 = qiandao2.reverse();
      let days = 1;
      for (let i = 0; i < qiandao2.length; i++) {
        let date1 = new Date(qiandao2[i]).getDate();
        let date2 = new Date(qiandao2[i + 1]).getDate();
        console.log(date1, date2);
        if (date1 - date2 == 1) {
          days++;
        } else {
          break;
        }
      }
      lianxuqiandaodays.value = days;
    }
    function qiandao(e) {
      qiandaolist.value = e.qiandaoarr;
      qiandaodays.value = e.qiandaoarr.length;
      iscontinuous();
      api_xj_sign.addView().then((result) => {
      });
      console.log(e);
    }
    function clickday(e) {
      console.log(e);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value == 1
      }, isLoading.value == 1 ? {
        b: common_vendor.o(qiandao),
        c: common_vendor.o(clickday),
        d: common_vendor.p({
          qiandaoinfo: qiandaolist.value
        })
      } : {}, {
        e: common_vendor.t(qiandaodays.value),
        f: common_vendor.t(lianxuqiandaodays.value)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8b70e6ba"]]);
wx.createPage(MiniProgramPage);
