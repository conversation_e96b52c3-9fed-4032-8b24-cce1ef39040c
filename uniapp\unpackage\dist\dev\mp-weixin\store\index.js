"use strict";
const common_vendor = require("../common/vendor.js");
const store_modules_app = require("./modules/app.js");
const store_modules_user = require("./modules/user.js");
const store_modules_theme = require("./modules/theme.js");
const store_getters = require("./getters.js");
const store = common_vendor.createStore({
  modules: {
    app: store_modules_app.app,
    user: store_modules_user.user,
    theme: store_modules_theme.theme
  },
  state: {},
  mutations: {},
  actions: {},
  getters: store_getters.getters
});
const store$1 = store;
exports.store = store$1;
